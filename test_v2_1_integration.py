#!/usr/bin/env python3
"""
BlendPro v2.1.0 Integration Test Script
Tests that all v2.1.0 components are properly integrated and functional
"""

import sys
import importlib
from typing import List, Dict, Any

def test_module_imports() -> Dict[str, Any]:
    """Test that all v2.1.0 modules can be imported"""
    results = {
        "success": True,
        "imported_modules": [],
        "failed_modules": [],
        "errors": []
    }
    
    # v2.1.0 modules to test
    test_modules = [
        # Provider management
        "config.providers",
        "config.agent_configs", 
        "utils.provider_manager",
        "utils.dynamic_api_client",
        
        # Core AI systems
        "core.agent_orchestrator",
        "core.collaborative_ai", 
        "core.cost_optimizer",
        
        # Enhanced vision
        "vision.depth_analyzer",
        "vision.spatial_reasoner",
        "vision.temporal_tracker", 
        "vision.immersive_vision_pipeline",
        
        # UI components
        "ui.provider_settings",
        "ui.agent_config_panel",
        "ui.vision_dashboard",
        "ui.performance_monitor"
    ]
    
    for module_name in test_modules:
        try:
            # Try to import the module
            full_name = f"BlendProV2.{module_name}"
            if full_name in sys.modules:
                importlib.reload(sys.modules[full_name])
            else:
                importlib.import_module(full_name)
            
            results["imported_modules"].append(module_name)
            print(f"✓ {module_name} imported successfully")
            
        except Exception as e:
            results["failed_modules"].append(module_name)
            results["errors"].append(f"{module_name}: {str(e)}")
            results["success"] = False
            print(f"✗ {module_name} import failed: {e}")
    
    return results

def test_settings_integration() -> Dict[str, Any]:
    """Test that v2.1.0 settings are properly configured"""
    results = {
        "success": True,
        "enabled_features": [],
        "disabled_features": [],
        "errors": []
    }
    
    try:
        from BlendProV2.config.settings import get_settings
        settings = get_settings()
        
        # Check v2.1.0 feature flags
        v2_1_features = {
            "enable_multi_agent_system": "Multi-Agent System",
            "enable_collaborative_analysis": "Collaborative Analysis", 
            "enable_enhanced_vision": "Enhanced Vision",
            "enable_depth_analysis": "Depth Analysis",
            "enable_spatial_reasoning": "Spatial Reasoning",
            "enable_temporal_tracking": "Temporal Tracking",
            "enable_cost_tracking": "Cost Tracking"
        }
        
        for setting_name, feature_name in v2_1_features.items():
            if hasattr(settings, setting_name):
                if getattr(settings, setting_name):
                    results["enabled_features"].append(feature_name)
                    print(f"✓ {feature_name} is enabled")
                else:
                    results["disabled_features"].append(feature_name)
                    print(f"⚠ {feature_name} is disabled")
            else:
                results["errors"].append(f"Setting {setting_name} not found")
                results["success"] = False
                print(f"✗ Setting {setting_name} not found")
        
    except Exception as e:
        results["errors"].append(f"Settings test failed: {str(e)}")
        results["success"] = False
        print(f"✗ Settings test failed: {e}")
    
    return results

def test_provider_system() -> Dict[str, Any]:
    """Test provider management system"""
    results = {
        "success": True,
        "providers_loaded": [],
        "errors": []
    }
    
    try:
        from BlendProV2.config.providers import get_default_providers
        from BlendProV2.utils.provider_manager import get_provider_manager
        
        # Test default providers
        default_providers = get_default_providers()
        results["providers_loaded"] = list(default_providers.keys())
        print(f"✓ Default providers loaded: {', '.join(default_providers.keys())}")
        
        # Test provider manager
        provider_manager = get_provider_manager()
        print("✓ Provider manager initialized")
        
    except Exception as e:
        results["errors"].append(f"Provider system test failed: {str(e)}")
        results["success"] = False
        print(f"✗ Provider system test failed: {e}")
    
    return results

def test_agent_system() -> Dict[str, Any]:
    """Test agent orchestrator system"""
    results = {
        "success": True,
        "agents_available": [],
        "errors": []
    }
    
    try:
        from BlendProV2.config.agent_configs import AgentType, get_default_agent_configurations
        from BlendProV2.core.agent_orchestrator import get_agent_orchestrator
        
        # Test agent types
        agent_types = [agent.value for agent in AgentType]
        results["agents_available"] = agent_types
        print(f"✓ Agent types available: {', '.join(agent_types)}")
        
        # Test agent configurations
        default_configs = get_default_agent_configurations()
        print(f"✓ Default agent configurations loaded: {len(default_configs)} agents")
        
        # Test agent orchestrator
        orchestrator = get_agent_orchestrator()
        print("✓ Agent orchestrator initialized")
        
    except Exception as e:
        results["errors"].append(f"Agent system test failed: {str(e)}")
        results["success"] = False
        print(f"✗ Agent system test failed: {e}")
    
    return results

def run_integration_tests() -> Dict[str, Any]:
    """Run all integration tests"""
    print("=" * 60)
    print("BlendPro v2.1.0 Integration Test")
    print("=" * 60)
    
    all_results = {
        "overall_success": True,
        "module_imports": {},
        "settings_integration": {},
        "provider_system": {},
        "agent_system": {}
    }
    
    # Test module imports
    print("\n1. Testing Module Imports...")
    print("-" * 30)
    all_results["module_imports"] = test_module_imports()
    if not all_results["module_imports"]["success"]:
        all_results["overall_success"] = False
    
    # Test settings integration
    print("\n2. Testing Settings Integration...")
    print("-" * 30)
    all_results["settings_integration"] = test_settings_integration()
    if not all_results["settings_integration"]["success"]:
        all_results["overall_success"] = False
    
    # Test provider system
    print("\n3. Testing Provider System...")
    print("-" * 30)
    all_results["provider_system"] = test_provider_system()
    if not all_results["provider_system"]["success"]:
        all_results["overall_success"] = False
    
    # Test agent system
    print("\n4. Testing Agent System...")
    print("-" * 30)
    all_results["agent_system"] = test_agent_system()
    if not all_results["agent_system"]["success"]:
        all_results["overall_success"] = False
    
    # Summary
    print("\n" + "=" * 60)
    if all_results["overall_success"]:
        print("🎉 ALL TESTS PASSED - BlendPro v2.1.0 is ready!")
    else:
        print("❌ SOME TESTS FAILED - Check errors above")
    print("=" * 60)
    
    return all_results

if __name__ == "__main__":
    # This script is designed to be run from within Blender's Python environment
    # or with BlendPro modules in the Python path
    run_integration_tests()
