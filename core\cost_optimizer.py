"""
Cost-Benefit Optimizer for BlendPro v2.1.0
Optimization system for collaborative analysis cost management
"""

import time
import statistics
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..config.settings import get_settings
from ..utils.provider_manager import get_provider_manager
from ..utils.logger import get_logger
from .collaborative_ai import CollaborativeConfig, ConsensusMethod, DisagreementResolution

class OptimizationStrategy(Enum):
    """Cost optimization strategies"""
    MINIMIZE_COST = "minimize_cost"
    MAXIMIZE_QUALITY = "maximize_quality"
    BALANCED = "balanced"
    ADAPTIVE = "adaptive"

class CostTier(Enum):
    """Cost tiers for different use cases"""
    BUDGET = "budget"      # < $0.01 per analysis
    STANDARD = "standard"  # $0.01 - $0.10 per analysis
    PREMIUM = "premium"    # $0.10 - $1.00 per analysis
    ENTERPRISE = "enterprise"  # > $1.00 per analysis

@dataclass
class OptimizationResult:
    """Result of cost optimization"""
    recommended_config: CollaborativeConfig
    estimated_cost: float
    estimated_quality: float
    cost_tier: CostTier
    optimization_strategy: OptimizationStrategy
    model_selection_reasoning: str
    confidence_threshold_adjustment: float
    expected_response_time: float

@dataclass
class CostBenefit:
    """Cost-benefit analysis result"""
    cost_per_token: float
    quality_score: float
    speed_score: float
    reliability_score: float
    overall_score: float
    cost_efficiency: float

class CostOptimizer:
    """Optimizes collaborative AI configurations for cost-benefit"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger("BlendPro.CostOptimizer")
        self.provider_manager = get_provider_manager()
        
        # Cost tracking
        self._cost_history: List[Dict[str, Any]] = []
        self._model_cost_performance: Dict[str, CostBenefit] = {}
        
        # Optimization parameters
        self._quality_weights = {
            "confidence": 0.4,
            "consistency": 0.3,
            "completeness": 0.2,
            "accuracy": 0.1
        }
    
    def optimize_for_budget(
        self, 
        max_budget: float, 
        quality_threshold: float = 0.7,
        strategy: OptimizationStrategy = OptimizationStrategy.BALANCED
    ) -> OptimizationResult:
        """Optimize collaborative configuration for given budget"""
        
        try:
            # Determine cost tier
            cost_tier = self._determine_cost_tier(max_budget)
            
            # Get available models within budget
            available_models = self._get_models_within_budget(max_budget)
            
            if not available_models:
                # Fallback to cheapest available models
                available_models = self._get_cheapest_models(2)
            
            # Select optimal model combination
            selected_models = self._select_optimal_models(
                available_models, max_budget, quality_threshold, strategy
            )
            
            # Determine optimal consensus method
            consensus_method = self._select_consensus_method(selected_models, strategy)
            
            # Calculate confidence threshold adjustment
            confidence_adjustment = self._calculate_confidence_adjustment(
                selected_models, quality_threshold
            )
            
            # Create optimized configuration
            config = CollaborativeConfig(
                models=selected_models,
                consensus_method=consensus_method,
                disagreement_resolution=DisagreementResolution.HIGHEST_CONFIDENCE,
                confidence_threshold=max(0.5, quality_threshold - confidence_adjustment),
                max_cost_per_analysis=max_budget,
                enable_parallel_execution=len(selected_models) <= 3  # Parallel for small sets
            )
            
            # Estimate performance
            estimated_cost = self._estimate_config_cost(config)
            estimated_quality = self._estimate_config_quality(config)
            expected_time = self._estimate_response_time(config)
            
            # Generate reasoning
            reasoning = self._generate_selection_reasoning(
                selected_models, strategy, cost_tier, max_budget
            )
            
            return OptimizationResult(
                recommended_config=config,
                estimated_cost=estimated_cost,
                estimated_quality=estimated_quality,
                cost_tier=cost_tier,
                optimization_strategy=strategy,
                model_selection_reasoning=reasoning,
                confidence_threshold_adjustment=confidence_adjustment,
                expected_response_time=expected_time
            )
            
        except Exception as e:
            self.logger.error(f"Cost optimization failed: {e}")
            # Return fallback configuration
            return self._get_fallback_optimization(max_budget)
    
    def _determine_cost_tier(self, budget: float) -> CostTier:
        """Determine cost tier based on budget"""
        
        if budget < 0.01:
            return CostTier.BUDGET
        elif budget < 0.10:
            return CostTier.STANDARD
        elif budget < 1.00:
            return CostTier.PREMIUM
        else:
            return CostTier.ENTERPRISE
    
    def _get_models_within_budget(self, budget: float) -> List[Tuple[str, str, float]]:
        """Get models that fit within budget"""
        
        models_with_cost = []
        
        try:
            providers = self.provider_manager.get_active_providers()
            
            for provider_name, provider in providers.items():
                for model in provider.supported_models:
                    # Estimate cost per request for this model
                    estimated_cost = self._estimate_model_cost(provider, model)
                    
                    if estimated_cost <= budget:
                        models_with_cost.append((provider_name, model, estimated_cost))
            
            # Sort by cost (cheapest first)
            models_with_cost.sort(key=lambda x: x[2])
            
        except Exception as e:
            self.logger.error(f"Error getting models within budget: {e}")
        
        return models_with_cost
    
    def _estimate_model_cost(self, provider, model: str) -> float:
        """Estimate cost per request for a model"""
        
        # Base cost calculation
        avg_input_tokens = 500  # Estimated average
        avg_output_tokens = 300  # Estimated average
        
        input_cost = (avg_input_tokens / 1000) * provider.cost_config.input_cost_per_1k_tokens
        output_cost = (avg_output_tokens / 1000) * provider.cost_config.output_cost_per_1k_tokens
        
        return input_cost + output_cost
    
    def _get_cheapest_models(self, count: int) -> List[Tuple[str, str]]:
        """Get cheapest available models"""
        
        try:
            providers = self.provider_manager.get_active_providers()
            model_costs = []
            
            for provider_name, provider in providers.items():
                for model in provider.supported_models:
                    cost = self._estimate_model_cost(provider, model)
                    model_costs.append((provider_name, model, cost))
            
            # Sort by cost and return cheapest
            model_costs.sort(key=lambda x: x[2])
            
            return [(provider, model) for provider, model, _ in model_costs[:count]]
            
        except Exception as e:
            self.logger.error(f"Error getting cheapest models: {e}")
            return [("openai", "gpt-4o-mini")]
    
    def _select_optimal_models(
        self, 
        available_models: List[Tuple[str, str, float]], 
        budget: float,
        quality_threshold: float,
        strategy: OptimizationStrategy
    ) -> List[Tuple[str, str]]:
        """Select optimal model combination"""
        
        if not available_models:
            return [("openai", "gpt-4o-mini")]
        
        if strategy == OptimizationStrategy.MINIMIZE_COST:
            # Select cheapest models that meet minimum quality
            selected = []
            total_cost = 0.0
            
            for provider, model, cost in available_models:
                if total_cost + cost <= budget:
                    selected.append((provider, model))
                    total_cost += cost
                    
                    if len(selected) >= 3:  # Limit to 3 models
                        break
            
            return selected or [(available_models[0][0], available_models[0][1])]
        
        elif strategy == OptimizationStrategy.MAXIMIZE_QUALITY:
            # Select best models within budget
            # For now, assume more expensive models are better
            selected = []
            total_cost = 0.0
            
            # Sort by cost (most expensive first for quality)
            sorted_models = sorted(available_models, key=lambda x: x[2], reverse=True)
            
            for provider, model, cost in sorted_models:
                if total_cost + cost <= budget:
                    selected.append((provider, model))
                    total_cost += cost
                    
                    if len(selected) >= 3:
                        break
            
            return selected or [(available_models[0][0], available_models[0][1])]
        
        else:  # BALANCED or ADAPTIVE
            # Balance cost and quality
            selected = []
            total_cost = 0.0
            
            # Select diverse models (different providers if possible)
            used_providers = set()
            
            for provider, model, cost in available_models:
                if total_cost + cost <= budget:
                    if provider not in used_providers or len(selected) < 2:
                        selected.append((provider, model))
                        used_providers.add(provider)
                        total_cost += cost
                        
                        if len(selected) >= 3:
                            break
            
            return selected or [(available_models[0][0], available_models[0][1])]
    
    def _select_consensus_method(
        self, 
        models: List[Tuple[str, str]], 
        strategy: OptimizationStrategy
    ) -> ConsensusMethod:
        """Select optimal consensus method"""
        
        if len(models) == 1:
            return ConsensusMethod.CONFIDENCE_BASED
        
        if strategy == OptimizationStrategy.MINIMIZE_COST:
            # Simple methods are cheaper
            return ConsensusMethod.HIGHEST_CONFIDENCE
        
        elif strategy == OptimizationStrategy.MAXIMIZE_QUALITY:
            # More sophisticated methods for quality
            return ConsensusMethod.WEIGHTED_AVERAGE
        
        else:  # BALANCED
            return ConsensusMethod.CONFIDENCE_BASED
    
    def _calculate_confidence_adjustment(
        self, 
        models: List[Tuple[str, str]], 
        quality_threshold: float
    ) -> float:
        """Calculate confidence threshold adjustment"""
        
        # Adjust based on number of models and their estimated quality
        if len(models) == 1:
            return 0.1  # Single model needs higher confidence
        elif len(models) == 2:
            return 0.05  # Two models can have slightly lower confidence
        else:
            return 0.0  # Multiple models can use standard confidence
    
    def _estimate_config_cost(self, config: CollaborativeConfig) -> float:
        """Estimate cost for a configuration"""
        
        total_cost = 0.0
        
        for provider_name, model_name in config.models:
            provider = self.provider_manager.get_provider(provider_name)
            if provider:
                model_cost = self._estimate_model_cost(provider, model_name)
                total_cost += model_cost
        
        return total_cost
    
    def _estimate_config_quality(self, config: CollaborativeConfig) -> float:
        """Estimate quality score for a configuration"""
        
        # Simple quality estimation based on number of models and consensus method
        base_quality = 0.7
        
        # More models generally mean better quality
        model_bonus = min(0.2, len(config.models) * 0.1)
        
        # Consensus method bonus
        consensus_bonus = {
            ConsensusMethod.HIGHEST_CONFIDENCE: 0.0,
            ConsensusMethod.CONFIDENCE_BASED: 0.05,
            ConsensusMethod.WEIGHTED_AVERAGE: 0.1,
            ConsensusMethod.MAJORITY_VOTE: 0.08
        }.get(config.consensus_method, 0.0)
        
        return min(1.0, base_quality + model_bonus + consensus_bonus)
    
    def _estimate_response_time(self, config: CollaborativeConfig) -> float:
        """Estimate response time for a configuration"""
        
        if config.enable_parallel_execution:
            # Parallel execution - time is max of individual models
            return 5.0 + len(config.models) * 0.5  # Base time + overhead
        else:
            # Sequential execution - time is sum of individual models
            return len(config.models) * 3.0  # Estimated time per model
    
    def _generate_selection_reasoning(
        self, 
        models: List[Tuple[str, str]], 
        strategy: OptimizationStrategy,
        cost_tier: CostTier,
        budget: float
    ) -> str:
        """Generate human-readable reasoning for model selection"""
        
        reasoning_parts = []
        
        reasoning_parts.append(f"Selected {len(models)} models for {cost_tier.value} tier analysis (${budget:.4f} budget)")
        
        if strategy == OptimizationStrategy.MINIMIZE_COST:
            reasoning_parts.append("Prioritized cost minimization - selected cheapest available models")
        elif strategy == OptimizationStrategy.MAXIMIZE_QUALITY:
            reasoning_parts.append("Prioritized quality maximization - selected best models within budget")
        else:
            reasoning_parts.append("Balanced cost and quality - selected diverse, cost-effective models")
        
        # List selected models
        model_list = ", ".join([f"{provider}/{model}" for provider, model in models])
        reasoning_parts.append(f"Models: {model_list}")
        
        return ". ".join(reasoning_parts)
    
    def _get_fallback_optimization(self, budget: float) -> OptimizationResult:
        """Get fallback optimization result"""
        
        config = CollaborativeConfig(
            models=[("openai", "gpt-4o-mini")],
            consensus_method=ConsensusMethod.CONFIDENCE_BASED,
            max_cost_per_analysis=budget
        )
        
        return OptimizationResult(
            recommended_config=config,
            estimated_cost=0.001,
            estimated_quality=0.7,
            cost_tier=CostTier.BUDGET,
            optimization_strategy=OptimizationStrategy.MINIMIZE_COST,
            model_selection_reasoning="Fallback to single budget model",
            confidence_threshold_adjustment=0.1,
            expected_response_time=3.0
        )
    
    def track_actual_cost(self, config: CollaborativeConfig, actual_cost: float, quality_score: float):
        """Track actual cost and quality for learning"""
        
        self._cost_history.append({
            "timestamp": time.time(),
            "config": config,
            "actual_cost": actual_cost,
            "quality_score": quality_score,
            "models": config.models
        })
        
        # Update model performance tracking
        for provider_name, model_name in config.models:
            model_id = f"{provider_name}/{model_name}"
            
            if model_id not in self._model_cost_performance:
                self._model_cost_performance[model_id] = CostBenefit(
                    cost_per_token=0.0,
                    quality_score=0.0,
                    speed_score=0.0,
                    reliability_score=0.0,
                    overall_score=0.0,
                    cost_efficiency=0.0
                )
            
            # Update with exponential moving average
            alpha = 0.1
            perf = self._model_cost_performance[model_id]
            
            estimated_tokens = 800  # Rough estimate
            cost_per_token = actual_cost / estimated_tokens
            
            perf.cost_per_token = (1 - alpha) * perf.cost_per_token + alpha * cost_per_token
            perf.quality_score = (1 - alpha) * perf.quality_score + alpha * quality_score
            perf.cost_efficiency = quality_score / (cost_per_token + 0.0001)  # Avoid division by zero
    
    def get_cost_analysis_report(self) -> Dict[str, Any]:
        """Get comprehensive cost analysis report"""
        
        if not self._cost_history:
            return {"message": "No cost history available"}
        
        total_analyses = len(self._cost_history)
        total_cost = sum(entry["actual_cost"] for entry in self._cost_history)
        avg_cost = total_cost / total_analyses
        avg_quality = statistics.mean([entry["quality_score"] for entry in self._cost_history])
        
        # Cost efficiency by tier
        tier_analysis = {}
        for entry in self._cost_history:
            cost = entry["actual_cost"]
            tier = self._determine_cost_tier(cost)
            
            if tier.value not in tier_analysis:
                tier_analysis[tier.value] = {"count": 0, "total_cost": 0.0, "total_quality": 0.0}
            
            tier_analysis[tier.value]["count"] += 1
            tier_analysis[tier.value]["total_cost"] += cost
            tier_analysis[tier.value]["total_quality"] += entry["quality_score"]
        
        # Calculate averages for each tier
        for tier_data in tier_analysis.values():
            tier_data["avg_cost"] = tier_data["total_cost"] / tier_data["count"]
            tier_data["avg_quality"] = tier_data["total_quality"] / tier_data["count"]
            tier_data["cost_efficiency"] = tier_data["avg_quality"] / tier_data["avg_cost"]
        
        return {
            "total_analyses": total_analyses,
            "total_cost": total_cost,
            "average_cost": avg_cost,
            "average_quality": avg_quality,
            "overall_cost_efficiency": avg_quality / avg_cost,
            "tier_analysis": tier_analysis,
            "model_performance": {
                model_id: {
                    "cost_per_token": perf.cost_per_token,
                    "quality_score": perf.quality_score,
                    "cost_efficiency": perf.cost_efficiency
                }
                for model_id, perf in self._model_cost_performance.items()
            }
        }

# Global instance
_cost_optimizer = None

def get_cost_optimizer() -> CostOptimizer:
    """Get global cost optimizer instance"""
    global _cost_optimizer
    if _cost_optimizer is None:
        _cost_optimizer = CostOptimizer()
    return _cost_optimizer
