"""
Vision Analysis Dashboard for BlendPro v2.1.0
User interface for displaying immersive vision results and insights
"""

import bpy
from bpy.types import Panel, Operator, PropertyGroup
from bpy.props import StringProperty, BoolProperty, EnumProperty, FloatProperty, IntProperty
from typing import List, Dict, Any, Optional

from ..vision.immersive_vision_pipeline import get_immersive_vision_pipeline, AnalysisMode
from ..vision.multi_modal_vision import get_multi_modal_vision
from ..utils.logger import get_logger

class BLENDPRO_PG_VisionSettings(PropertyGroup):
    """Property group for vision analysis settings"""
    
    analysis_mode: EnumProperty(
        name="Analysis Mode",
        description="Vision analysis mode",
        items=[
            ('quick', 'Quick', 'Fast analysis with basic insights'),
            ('standard', 'Standard', 'Standard analysis with depth and spatial'),
            ('comprehensive', 'Comprehensive', 'Full analysis including temporal tracking'),
            ('custom', 'Custom', 'Custom configuration')
        ],
        default='standard'
    )
    
    include_depth: BoolProperty(
        name="Include Depth Analysis",
        description="Include depth buffer analysis",
        default=True
    )
    
    include_spatial: BoolProperty(
        name="Include Spatial Analysis",
        description="Include spatial relationship analysis",
        default=True
    )
    
    include_temporal: BoolProperty(
        name="Include Temporal Analysis",
        description="Include temporal change tracking",
        default=False
    )
    
    depth_sample_density: FloatProperty(
        name="Depth Sample Density",
        description="Density of depth point sampling",
        default=1.0,
        min=0.1,
        max=2.0,
        precision=1
    )
    
    auto_refresh: BoolProperty(
        name="Auto Refresh",
        description="Automatically refresh analysis when scene changes",
        default=False
    )
    
    show_insights: BoolProperty(
        name="Show Insights",
        description="Display generated insights",
        default=True
    )
    
    show_recommendations: BoolProperty(
        name="Show Recommendations",
        description="Display actionable recommendations",
        default=True
    )
    
    show_performance: BoolProperty(
        name="Show Performance Metrics",
        description="Display performance metrics",
        default=False
    )

class BLENDPRO_OT_RunVisionAnalysis(Operator):
    """Run vision analysis"""
    bl_idname = "blendpro.run_vision_analysis"
    bl_label = "Run Vision Analysis"
    bl_description = "Run immersive vision analysis on current scene"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            settings = context.scene.blendpro_vision_settings
            multi_modal_vision = get_multi_modal_vision()
            
            # Run enhanced vision analysis
            result = multi_modal_vision.analyze_scene_with_enhanced_vision(
                context,
                analysis_mode=AnalysisMode(settings.analysis_mode),
                include_legacy=True
            )
            
            if result.get('enhanced_vision_used', False):
                # Store results in scene for display
                context.scene.blendpro_vision_result = str(result)
                
                immersive_result = result.get('immersive_result')
                if immersive_result and immersive_result.success:
                    insight_count = len(immersive_result.immersive_insights)
                    recommendation_count = len(immersive_result.recommendations)
                    analysis_time = immersive_result.total_analysis_time
                    
                    self.report({'INFO'}, f"Vision analysis complete: {insight_count} insights, {recommendation_count} recommendations ({analysis_time:.2f}s)")
                else:
                    self.report({'WARNING'}, "Vision analysis completed with limited results")
            else:
                self.report({'WARNING'}, "Used legacy vision analysis - enhanced features unavailable")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Vision analysis failed: {str(e)}")
            return {'CANCELLED'}

class BLENDPRO_OT_ClearVisionCache(Operator):
    """Clear vision analysis cache"""
    bl_idname = "blendpro.clear_vision_cache"
    bl_label = "Clear Cache"
    bl_description = "Clear all vision analysis caches"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            multi_modal_vision = get_multi_modal_vision()
            result = multi_modal_vision.clear_all_vision_caches()
            
            if result.get('success', False):
                self.report({'INFO'}, "Vision caches cleared successfully")
            else:
                self.report({'ERROR'}, f"Failed to clear caches: {result.get('error', 'Unknown error')}")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Error clearing caches: {str(e)}")
            return {'CANCELLED'}

class BLENDPRO_OT_ExportVisionResults(Operator):
    """Export vision analysis results"""
    bl_idname = "blendpro.export_vision_results"
    bl_label = "Export Results"
    bl_description = "Export vision analysis results to file"
    bl_options = {'REGISTER'}
    
    filepath: StringProperty(
        name="File Path",
        description="Path to export file",
        default="vision_analysis.json",
        subtype='FILE_PATH'
    )
    
    def execute(self, context):
        try:
            import json
            
            # Get stored results
            if hasattr(context.scene, 'blendpro_vision_result'):
                result_str = context.scene.blendpro_vision_result
                result = eval(result_str)  # Note: In production, use proper JSON serialization
                
                # Export to file
                with open(self.filepath, 'w') as f:
                    json.dump(result, f, indent=2, default=str)
                
                self.report({'INFO'}, f"Vision results exported to {self.filepath}")
            else:
                self.report({'ERROR'}, "No vision analysis results to export")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Export failed: {str(e)}")
            return {'CANCELLED'}
    
    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

class BLENDPRO_PT_VisionDashboard(Panel):
    """Vision Analysis Dashboard Panel"""
    bl_label = "Vision Analysis Dashboard"
    bl_idname = "BLENDPRO_PT_vision_dashboard"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "BlendPro"
    bl_parent_id = "BLENDPRO_PT_main_panel"
    
    def draw(self, context):
        layout = self.layout
        settings = context.scene.blendpro_vision_settings
        
        # Analysis controls
        box = layout.box()
        box.label(text="Vision Analysis Controls", icon='CAMERA_DATA')
        
        col = box.column()
        col.prop(settings, "analysis_mode")
        
        # Custom settings (if custom mode)
        if settings.analysis_mode == 'custom':
            col.separator()
            col.label(text="Custom Settings", icon='SETTINGS')
            col.prop(settings, "include_depth")
            col.prop(settings, "include_spatial")
            col.prop(settings, "include_temporal")
            col.prop(settings, "depth_sample_density")
        
        # Analysis options
        col.separator()
        col.prop(settings, "auto_refresh")
        
        # Run analysis button
        col.separator()
        row = col.row(align=True)
        row.operator("blendpro.run_vision_analysis", text="Run Analysis", icon='PLAY')
        row.operator("blendpro.clear_vision_cache", text="Clear Cache", icon='TRASH')
        
        layout.separator()
        
        # Results display
        self._draw_results(layout, context, settings)
    
    def _draw_results(self, layout, context, settings):
        """Draw analysis results"""
        
        # Check if we have results
        if not hasattr(context.scene, 'blendpro_vision_result'):
            box = layout.box()
            box.label(text="No Analysis Results", icon='INFO')
            box.label(text="Run vision analysis to see results")
            return
        
        try:
            result_str = context.scene.blendpro_vision_result
            result = eval(result_str)  # Note: In production, use proper JSON serialization
            
            # Scene understanding
            box = layout.box()
            box.label(text="Scene Understanding", icon='WORLD')
            
            scene_understanding = result.get('scene_understanding', 'No understanding available')
            # Split long text into multiple lines
            lines = scene_understanding.split('. ')
            for line in lines[:3]:  # Show first 3 sentences
                if line.strip():
                    box.label(text=line.strip() + '.')
            
            # Insights
            if settings.show_insights:
                immersive_result = result.get('immersive_result')
                if immersive_result and hasattr(immersive_result, 'immersive_insights'):
                    insights = immersive_result.immersive_insights
                    
                    if insights:
                        box = layout.box()
                        box.label(text=f"Insights ({len(insights)})", icon='LIGHTBULB')
                        
                        # Show high priority insights
                        high_priority = [i for i in insights if getattr(i, 'priority', 0) >= 4]
                        for insight in high_priority[:3]:  # Show top 3
                            row = box.row()
                            priority_icon = 'ERROR' if insight.priority >= 4 else 'INFO'
                            row.label(text=insight.title, icon=priority_icon)
                            
                            # Show description in smaller text
                            desc_box = box.box()
                            desc_box.scale_y = 0.8
                            desc_box.label(text=insight.description)
            
            # Recommendations
            if settings.show_recommendations:
                recommendations = result.get('recommendations', [])
                
                if recommendations:
                    box = layout.box()
                    box.label(text=f"Recommendations ({len(recommendations)})", icon='TOOL_SETTINGS')
                    
                    for rec in recommendations[:5]:  # Show top 5
                        row = box.row()
                        row.label(text="•", icon='FORWARD')
                        
                        # Split long recommendations
                        if len(rec) > 50:
                            rec = rec[:47] + "..."
                        row.label(text=rec)
            
            # Performance metrics
            if settings.show_performance:
                performance = result.get('performance_metrics', {})
                
                if performance:
                    box = layout.box()
                    box.label(text="Performance Metrics", icon='GRAPH')
                    
                    total_time = performance.get('total_time', 0)
                    box.label(text=f"Total Analysis Time: {total_time:.2f}s")
                    
                    component_times = performance.get('component_times', {})
                    for component, time_taken in component_times.items():
                        box.label(text=f"{component.title()}: {time_taken:.2f}s")
            
            # Export button
            layout.separator()
            layout.operator("blendpro.export_vision_results", text="Export Results", icon='EXPORT')
            
        except Exception as e:
            box = layout.box()
            box.label(text="Error displaying results", icon='ERROR')
            box.label(text=str(e))

class BLENDPRO_PT_VisionSettings(Panel):
    """Vision Settings Panel"""
    bl_label = "Vision Settings"
    bl_idname = "BLENDPRO_PT_vision_settings"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "BlendPro"
    bl_parent_id = "BLENDPRO_PT_vision_dashboard"
    
    def draw(self, context):
        layout = self.layout
        settings = context.scene.blendpro_vision_settings
        
        # Display options
        box = layout.box()
        box.label(text="Display Options", icon='RESTRICT_VIEW_OFF')
        
        col = box.column()
        col.prop(settings, "show_insights")
        col.prop(settings, "show_recommendations")
        col.prop(settings, "show_performance")

def register():
    """Register UI classes"""
    bpy.utils.register_class(BLENDPRO_PG_VisionSettings)
    bpy.utils.register_class(BLENDPRO_OT_RunVisionAnalysis)
    bpy.utils.register_class(BLENDPRO_OT_ClearVisionCache)
    bpy.utils.register_class(BLENDPRO_OT_ExportVisionResults)
    bpy.utils.register_class(BLENDPRO_PT_VisionDashboard)
    bpy.utils.register_class(BLENDPRO_PT_VisionSettings)

    # Register property groups
    bpy.types.Scene.blendpro_vision_settings = bpy.props.PointerProperty(type=BLENDPRO_PG_VisionSettings)
    bpy.types.Scene.blendpro_vision_result = bpy.props.StringProperty(name="Vision Result", default="")

def unregister():
    """Unregister UI classes"""
    bpy.utils.unregister_class(BLENDPRO_PT_VisionSettings)
    bpy.utils.unregister_class(BLENDPRO_PT_VisionDashboard)
    bpy.utils.unregister_class(BLENDPRO_OT_ExportVisionResults)
    bpy.utils.unregister_class(BLENDPRO_OT_ClearVisionCache)
    bpy.utils.unregister_class(BLENDPRO_OT_RunVisionAnalysis)
    bpy.utils.unregister_class(BLENDPRO_PG_VisionSettings)

    # Unregister property groups
    del bpy.types.Scene.blendpro_vision_settings
    del bpy.types.Scene.blendpro_vision_result
