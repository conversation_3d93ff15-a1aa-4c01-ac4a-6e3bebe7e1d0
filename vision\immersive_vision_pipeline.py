"""
Immersive Vision Pipeline for BlendPro v2.1.0
Integrated pipeline combining depth analysis, spatial reasoning, and temporal tracking
"""

import time
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..config.settings import get_settings
from ..utils.logger import get_logger
from .depth_analyzer import get_depth_analyzer, DepthAnalysisResult
from .spatial_reasoner import get_spatial_reasoner, SpatialAnalysisResult
from .temporal_tracker import get_temporal_tracker, TemporalAnalysisResult
from .scene_analyzer import get_scene_analyzer
from .screenshot_manager import get_screenshot_manager

class AnalysisMode(Enum):
    """Analysis modes for the vision pipeline"""
    QUICK = "quick"           # Basic analysis, fast
    STANDARD = "standard"     # Standard analysis with depth and spatial
    COMPREHENSIVE = "comprehensive"  # Full analysis including temporal
    CUSTOM = "custom"         # Custom configuration

@dataclass
class PipelineConfiguration:
    """Configuration for the vision pipeline"""
    include_depth: bool = True
    include_spatial: bool = True
    include_temporal: bool = True
    include_screenshot: bool = True
    depth_sample_density: float = 1.0
    spatial_include_depth: bool = True
    temporal_include_spatial: bool = True
    temporal_include_depth: bool = False
    use_cache: bool = True
    max_analysis_time: float = 30.0  # seconds

@dataclass
class ImmersiveInsight:
    """Represents an immersive insight about the scene"""
    insight_type: str
    title: str
    description: str
    confidence: float
    supporting_data: Dict[str, Any]
    actionable: bool
    priority: int  # 1-5, 5 being highest

@dataclass
class ImmersiveVisionResult:
    """Complete result from immersive vision analysis"""
    # Core analysis results
    depth_result: Optional[DepthAnalysisResult]
    spatial_result: Optional[SpatialAnalysisResult]
    temporal_result: Optional[TemporalAnalysisResult]
    screenshot_data: Optional[Dict[str, Any]]
    scene_data: Optional[Dict[str, Any]]
    
    # Integrated insights
    immersive_insights: List[ImmersiveInsight]
    scene_understanding: str
    recommendations: List[str]
    
    # Metadata
    analysis_mode: AnalysisMode
    configuration: PipelineConfiguration
    total_analysis_time: float
    component_times: Dict[str, float]
    success: bool
    error: Optional[str] = None

class ImmersiveVisionPipeline:
    """Integrated vision pipeline for comprehensive 3D scene understanding"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger("BlendPro.ImmersiveVision")
        
        # Component analyzers
        self.depth_analyzer = get_depth_analyzer()
        self.spatial_reasoner = get_spatial_reasoner()
        self.temporal_tracker = get_temporal_tracker()
        self.scene_analyzer = get_scene_analyzer()
        self.screenshot_manager = get_screenshot_manager()
        
        # Pipeline cache
        self._pipeline_cache: Dict[str, Tuple[ImmersiveVisionResult, float]] = {}
        self._cache_timeout = 20.0  # seconds
    
    def analyze_scene_immersively(
        self, 
        context, 
        mode: AnalysisMode = AnalysisMode.STANDARD,
        custom_config: Optional[PipelineConfiguration] = None
    ) -> ImmersiveVisionResult:
        """Perform comprehensive immersive scene analysis"""
        
        start_time = time.time()
        
        # Get configuration based on mode
        config = self._get_configuration(mode, custom_config)
        
        # Generate cache key
        cache_key = self._generate_cache_key(context, mode, config)
        
        # Check cache
        if config.use_cache and cache_key in self._pipeline_cache:
            cached_result, timestamp = self._pipeline_cache[cache_key]
            if time.time() - timestamp < self._cache_timeout:
                self.logger.debug("Using cached immersive vision result")
                return cached_result
        
        try:
            component_times = {}
            
            # Initialize results
            depth_result = None
            spatial_result = None
            temporal_result = None
            screenshot_data = None
            scene_data = None
            
            # Capture screenshot if requested
            if config.include_screenshot:
                screenshot_start = time.time()
                screenshot_data = self.screenshot_manager.capture_viewport_screenshot(
                    context, use_cache=config.use_cache
                )
                component_times["screenshot"] = time.time() - screenshot_start
            
            # Analyze scene data
            scene_start = time.time()
            scene_data = self.scene_analyzer.analyze_scene(context, use_cache=config.use_cache)
            component_times["scene"] = time.time() - scene_start
            
            # Depth analysis
            if config.include_depth:
                depth_start = time.time()
                depth_result = self.depth_analyzer.analyze_depth(
                    context, 
                    sample_density=config.depth_sample_density,
                    use_cache=config.use_cache
                )
                component_times["depth"] = time.time() - depth_start
            
            # Spatial analysis
            if config.include_spatial:
                spatial_start = time.time()
                spatial_result = self.spatial_reasoner.analyze_spatial_relationships(
                    context,
                    include_depth=config.spatial_include_depth and depth_result is not None,
                    use_cache=config.use_cache
                )
                component_times["spatial"] = time.time() - spatial_start
            
            # Temporal analysis
            if config.include_temporal:
                temporal_start = time.time()
                temporal_result = self.temporal_tracker.analyze_temporal_changes(
                    context,
                    include_spatial=config.temporal_include_spatial,
                    include_depth=config.temporal_include_depth
                )
                component_times["temporal"] = time.time() - temporal_start
            
            # Generate immersive insights
            insights_start = time.time()
            immersive_insights = self._generate_immersive_insights(
                depth_result, spatial_result, temporal_result, scene_data
            )
            component_times["insights"] = time.time() - insights_start
            
            # Generate scene understanding
            understanding_start = time.time()
            scene_understanding = self._generate_scene_understanding(
                depth_result, spatial_result, temporal_result, scene_data, immersive_insights
            )
            component_times["understanding"] = time.time() - understanding_start
            
            # Generate recommendations
            recommendations_start = time.time()
            recommendations = self._generate_recommendations(
                depth_result, spatial_result, temporal_result, immersive_insights
            )
            component_times["recommendations"] = time.time() - recommendations_start
            
            total_time = time.time() - start_time
            
            result = ImmersiveVisionResult(
                depth_result=depth_result,
                spatial_result=spatial_result,
                temporal_result=temporal_result,
                screenshot_data=screenshot_data,
                scene_data=scene_data,
                immersive_insights=immersive_insights,
                scene_understanding=scene_understanding,
                recommendations=recommendations,
                analysis_mode=mode,
                configuration=config,
                total_analysis_time=total_time,
                component_times=component_times,
                success=True
            )
            
            # Cache result
            if config.use_cache:
                self._pipeline_cache[cache_key] = (result, time.time())
            
            return result
            
        except Exception as e:
            self.logger.error(f"Immersive vision analysis failed: {e}")
            return ImmersiveVisionResult(
                depth_result=None,
                spatial_result=None,
                temporal_result=None,
                screenshot_data=None,
                scene_data=None,
                immersive_insights=[],
                scene_understanding=f"Analysis failed: {str(e)}",
                recommendations=[],
                analysis_mode=mode,
                configuration=config,
                total_analysis_time=time.time() - start_time,
                component_times={},
                success=False,
                error=str(e)
            )
    
    def _get_configuration(
        self, 
        mode: AnalysisMode, 
        custom_config: Optional[PipelineConfiguration]
    ) -> PipelineConfiguration:
        """Get pipeline configuration based on mode"""
        
        if custom_config:
            return custom_config
        
        if mode == AnalysisMode.QUICK:
            return PipelineConfiguration(
                include_depth=False,
                include_spatial=True,
                include_temporal=False,
                include_screenshot=True,
                spatial_include_depth=False,
                temporal_include_spatial=False,
                temporal_include_depth=False,
                max_analysis_time=10.0
            )
        
        elif mode == AnalysisMode.STANDARD:
            return PipelineConfiguration(
                include_depth=True,
                include_spatial=True,
                include_temporal=False,
                include_screenshot=True,
                depth_sample_density=0.5,
                spatial_include_depth=True,
                temporal_include_spatial=False,
                temporal_include_depth=False,
                max_analysis_time=20.0
            )
        
        elif mode == AnalysisMode.COMPREHENSIVE:
            return PipelineConfiguration(
                include_depth=True,
                include_spatial=True,
                include_temporal=True,
                include_screenshot=True,
                depth_sample_density=1.0,
                spatial_include_depth=True,
                temporal_include_spatial=True,
                temporal_include_depth=True,
                max_analysis_time=30.0
            )
        
        else:  # CUSTOM or fallback
            return PipelineConfiguration()  # Default configuration

    def _generate_immersive_insights(
        self,
        depth_result: Optional[DepthAnalysisResult],
        spatial_result: Optional[SpatialAnalysisResult],
        temporal_result: Optional[TemporalAnalysisResult],
        scene_data: Optional[Dict[str, Any]]
    ) -> List[ImmersiveInsight]:
        """Generate immersive insights from analysis results"""

        insights = []

        try:
            # Depth-based insights
            if depth_result and depth_result.success:
                depth_stats = depth_result.depth_statistics

                if depth_stats.get('valid_pixel_ratio', 0) < 0.5:
                    insights.append(ImmersiveInsight(
                        insight_type="depth_coverage",
                        title="Limited Depth Information",
                        description=f"Only {depth_stats.get('valid_pixel_ratio', 0)*100:.1f}% of the scene has valid depth data. Consider adjusting camera position or adding more objects.",
                        confidence=0.8,
                        supporting_data={"depth_stats": depth_stats},
                        actionable=True,
                        priority=3
                    ))

                depth_range = depth_stats.get('max_depth', 0) - depth_stats.get('min_depth', 0)
                if depth_range > 100:
                    insights.append(ImmersiveInsight(
                        insight_type="depth_range",
                        title="Large Depth Range",
                        description=f"Scene has a large depth range ({depth_range:.1f} units). This may affect rendering performance and depth precision.",
                        confidence=0.7,
                        supporting_data={"depth_range": depth_range},
                        actionable=True,
                        priority=2
                    ))

            # Spatial-based insights
            if spatial_result and spatial_result.success:
                spatial_graph = spatial_result.spatial_graph

                if len(spatial_graph.objects) > 20:
                    insights.append(ImmersiveInsight(
                        insight_type="scene_complexity",
                        title="Complex Scene",
                        description=f"Scene contains {len(spatial_graph.objects)} objects. Consider organizing with collections or reducing complexity for better performance.",
                        confidence=0.9,
                        supporting_data={"object_count": len(spatial_graph.objects)},
                        actionable=True,
                        priority=3
                    ))

                if len(spatial_graph.spatial_clusters) > 0:
                    insights.append(ImmersiveInsight(
                        insight_type="spatial_organization",
                        title="Spatial Clusters Detected",
                        description=f"Found {len(spatial_graph.spatial_clusters)} clusters of related objects. This suggests good spatial organization.",
                        confidence=0.8,
                        supporting_data={"clusters": spatial_graph.spatial_clusters},
                        actionable=False,
                        priority=1
                    ))

                # Check for isolated objects
                isolated_objects = []
                for obj_name in spatial_graph.objects.keys():
                    obj_relationships = [r for r in spatial_graph.relationships
                                       if r.object_a == obj_name or r.object_b == obj_name]
                    if len(obj_relationships) == 0:
                        isolated_objects.append(obj_name)

                if isolated_objects:
                    insights.append(ImmersiveInsight(
                        insight_type="spatial_isolation",
                        title="Isolated Objects",
                        description=f"Found {len(isolated_objects)} isolated objects with no spatial relationships. Consider repositioning for better composition.",
                        confidence=0.6,
                        supporting_data={"isolated_objects": isolated_objects},
                        actionable=True,
                        priority=2
                    ))

            # Temporal-based insights
            if temporal_result and temporal_result.success:
                changes = temporal_result.changes

                if len(changes) > 10:
                    insights.append(ImmersiveInsight(
                        insight_type="high_activity",
                        title="High Scene Activity",
                        description=f"Detected {len(changes)} changes since last analysis. Scene is highly dynamic.",
                        confidence=0.9,
                        supporting_data={"change_count": len(changes)},
                        actionable=False,
                        priority=2
                    ))

                # Check for frequent object movements
                movement_changes = [c for c in changes if c.change_type.value in ['object_moved', 'object_rotated', 'object_scaled']]
                if len(movement_changes) > 5:
                    insights.append(ImmersiveInsight(
                        insight_type="frequent_movement",
                        title="Frequent Object Movement",
                        description=f"Detected {len(movement_changes)} object movements. Consider using keyframes for animation.",
                        confidence=0.7,
                        supporting_data={"movement_changes": len(movement_changes)},
                        actionable=True,
                        priority=3
                    ))

            # Scene-based insights
            if scene_data and not scene_data.get('error'):
                objects = scene_data.get('objects', [])
                materials = scene_data.get('materials', [])
                lights = scene_data.get('lights', [])

                if len(lights) == 0:
                    insights.append(ImmersiveInsight(
                        insight_type="lighting",
                        title="No Lights in Scene",
                        description="Scene has no light sources. Add lights for better visualization and rendering.",
                        confidence=0.9,
                        supporting_data={"light_count": 0},
                        actionable=True,
                        priority=4
                    ))

                if len(materials) == 0:
                    insights.append(ImmersiveInsight(
                        insight_type="materials",
                        title="No Materials",
                        description="Scene objects have no materials. Add materials for better visual quality.",
                        confidence=0.8,
                        supporting_data={"material_count": 0},
                        actionable=True,
                        priority=3
                    ))

                # Check for default objects
                default_objects = [obj for obj in objects if obj.get('name', '').startswith(('Cube', 'Sphere', 'Cylinder'))]
                if len(default_objects) > 3:
                    insights.append(ImmersiveInsight(
                        insight_type="default_objects",
                        title="Many Default Objects",
                        description=f"Found {len(default_objects)} default objects. Consider customizing or replacing with unique models.",
                        confidence=0.6,
                        supporting_data={"default_objects": [obj.get('name') for obj in default_objects]},
                        actionable=True,
                        priority=2
                    ))

        except Exception as e:
            self.logger.error(f"Insight generation failed: {e}")

        # Sort insights by priority (highest first)
        insights.sort(key=lambda x: x.priority, reverse=True)

        return insights

    def _generate_scene_understanding(
        self,
        depth_result: Optional[DepthAnalysisResult],
        spatial_result: Optional[SpatialAnalysisResult],
        temporal_result: Optional[TemporalAnalysisResult],
        scene_data: Optional[Dict[str, Any]],
        insights: List[ImmersiveInsight]
    ) -> str:
        """Generate comprehensive scene understanding"""

        understanding_parts = []

        try:
            # Scene overview
            if scene_data and not scene_data.get('error'):
                objects = scene_data.get('objects', [])
                understanding_parts.append(f"Scene Overview: {len(objects)} objects in a 3D environment")

                # Object types summary
                object_types = {}
                for obj in objects:
                    obj_type = obj.get('type', 'UNKNOWN')
                    object_types[obj_type] = object_types.get(obj_type, 0) + 1

                if object_types:
                    type_summary = ", ".join([f"{count} {obj_type.lower()}" for obj_type, count in object_types.items()])
                    understanding_parts.append(f"Object composition: {type_summary}")

            # Spatial understanding
            if spatial_result and spatial_result.success:
                spatial_graph = spatial_result.spatial_graph
                understanding_parts.append(f"Spatial analysis: {len(spatial_graph.relationships)} relationships between objects")

                if spatial_graph.spatial_clusters:
                    understanding_parts.append(f"Spatial organization: {len(spatial_graph.spatial_clusters)} clusters of related objects")

            # Depth understanding
            if depth_result and depth_result.success:
                depth_stats = depth_result.depth_statistics
                min_depth = depth_stats.get('min_depth', 0)
                max_depth = depth_stats.get('max_depth', 0)
                understanding_parts.append(f"Depth analysis: Scene extends from {min_depth:.1f} to {max_depth:.1f} units")

            # Temporal understanding
            if temporal_result and temporal_result.success:
                changes = temporal_result.changes
                if changes:
                    understanding_parts.append(f"Temporal analysis: {len(changes)} changes detected since last analysis")
                else:
                    understanding_parts.append("Temporal analysis: Scene is stable with no recent changes")

            # Key insights
            if insights:
                high_priority_insights = [i for i in insights if i.priority >= 4]
                if high_priority_insights:
                    understanding_parts.append(f"Key insights: {len(high_priority_insights)} high-priority recommendations available")

            return ". ".join(understanding_parts) + "."

        except Exception as e:
            return f"Scene understanding generation failed: {str(e)}"

    def _generate_recommendations(
        self,
        depth_result: Optional[DepthAnalysisResult],
        spatial_result: Optional[SpatialAnalysisResult],
        temporal_result: Optional[TemporalAnalysisResult],
        insights: List[ImmersiveInsight]
    ) -> List[str]:
        """Generate actionable recommendations"""

        recommendations = []

        try:
            # Extract actionable insights
            actionable_insights = [i for i in insights if i.actionable]

            for insight in actionable_insights[:5]:  # Top 5 recommendations
                recommendations.append(insight.description)

            # Add general recommendations based on analysis results
            if depth_result and depth_result.success:
                if len(depth_result.depth_regions) < 3:
                    recommendations.append("Consider adding objects at different depths for more interesting composition")

            if spatial_result and spatial_result.success:
                if len(spatial_result.spatial_graph.relationships) < len(spatial_result.spatial_graph.objects):
                    recommendations.append("Position objects closer together to create more spatial relationships")

            if temporal_result and temporal_result.success:
                if len(temporal_result.changes) == 0:
                    recommendations.append("Scene appears static - consider adding animation or dynamic elements")

        except Exception as e:
            self.logger.error(f"Recommendation generation failed: {e}")

        return recommendations[:10]  # Limit to 10 recommendations

    def _generate_cache_key(
        self,
        context,
        mode: AnalysisMode,
        config: PipelineConfiguration
    ) -> str:
        """Generate cache key for pipeline analysis"""

        try:
            cache_components = [
                mode.value,
                str(config.include_depth),
                str(config.include_spatial),
                str(config.include_temporal),
                str(config.depth_sample_density),
                str(len(context.scene.objects)),
                str(context.scene.frame_current)
            ]

            # Add viewport information if available
            if context.area and context.area.type == 'VIEW_3D':
                space = context.area.spaces.active
                if space and hasattr(space, 'region_3d') and space.region_3d:
                    view_location = space.region_3d.view_location
                    cache_components.append(f"{view_location.x:.1f}_{view_location.y:.1f}_{view_location.z:.1f}")

            return "_".join(cache_components)

        except Exception:
            return f"fallback_{time.time()}"

    def get_analysis_summary(self, result: ImmersiveVisionResult) -> str:
        """Get a comprehensive summary of the analysis"""

        if not result.success:
            return f"Analysis failed: {result.error}"

        summary_parts = []

        # Analysis overview
        summary_parts.append(f"Immersive Vision Analysis ({result.analysis_mode.value} mode)")
        summary_parts.append(f"Total analysis time: {result.total_analysis_time:.2f} seconds")

        # Component results
        if result.depth_result and result.depth_result.success:
            summary_parts.append(f"✓ Depth analysis: {len(result.depth_result.depth_regions)} regions, {len(result.depth_result.depth_points)} points")

        if result.spatial_result and result.spatial_result.success:
            summary_parts.append(f"✓ Spatial analysis: {result.spatial_result.object_count} objects, {result.spatial_result.relationship_count} relationships")

        if result.temporal_result and result.temporal_result.success:
            summary_parts.append(f"✓ Temporal analysis: {len(result.temporal_result.changes)} changes detected")

        # Insights summary
        if result.immersive_insights:
            high_priority = len([i for i in result.immersive_insights if i.priority >= 4])
            summary_parts.append(f"Generated {len(result.immersive_insights)} insights ({high_priority} high priority)")

        # Recommendations
        if result.recommendations:
            summary_parts.append(f"Provided {len(result.recommendations)} recommendations")

        return "\n".join(summary_parts)

    def get_insights_by_priority(self, result: ImmersiveVisionResult, min_priority: int = 1) -> List[ImmersiveInsight]:
        """Get insights filtered by minimum priority"""

        if not result.success:
            return []

        return [insight for insight in result.immersive_insights if insight.priority >= min_priority]

    def get_actionable_insights(self, result: ImmersiveVisionResult) -> List[ImmersiveInsight]:
        """Get only actionable insights"""

        if not result.success:
            return []

        return [insight for insight in result.immersive_insights if insight.actionable]

    def clear_cache(self):
        """Clear pipeline cache"""
        self._pipeline_cache.clear()
        self.logger.debug("Immersive vision pipeline cache cleared")

    def get_performance_metrics(self, result: ImmersiveVisionResult) -> Dict[str, Any]:
        """Get performance metrics from analysis"""

        if not result.success:
            return {}

        metrics = {
            "total_time": result.total_analysis_time,
            "component_times": result.component_times,
            "analysis_mode": result.analysis_mode.value,
            "cache_used": result.configuration.use_cache
        }

        # Add component-specific metrics
        if result.depth_result and result.depth_result.success:
            metrics["depth_analysis_time"] = result.depth_result.analysis_time
            metrics["depth_points_analyzed"] = len(result.depth_result.depth_points)

        if result.spatial_result and result.spatial_result.success:
            metrics["spatial_analysis_time"] = result.spatial_result.spatial_graph.analysis_time
            metrics["relationships_calculated"] = len(result.spatial_result.spatial_graph.relationships)

        if result.temporal_result and result.temporal_result.success:
            metrics["temporal_analysis_time"] = result.temporal_result.analysis_time
            metrics["changes_detected"] = len(result.temporal_result.changes)

        return metrics

# Global instance
_immersive_vision_pipeline = None

def get_immersive_vision_pipeline() -> ImmersiveVisionPipeline:
    """Get global immersive vision pipeline instance"""
    global _immersive_vision_pipeline
    if _immersive_vision_pipeline is None:
        _immersive_vision_pipeline = ImmersiveVisionPipeline()
    return _immersive_vision_pipeline
