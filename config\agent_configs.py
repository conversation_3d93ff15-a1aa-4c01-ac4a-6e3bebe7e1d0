"""
Agent Configuration Management for BlendPro v2.1.0
Defines agent types, configurations, and agent-model mapping
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import json

class AgentType(Enum):
    """Agent type enumeration"""
    VISION_SPECIALIST = "vision_specialist"
    CODE_ARCHITECT = "code_architect"
    SCENE_OPTIMIZER = "scene_optimizer"
    UX_ADVISOR = "ux_advisor"
    PERFORMANCE_ANALYST = "performance_analyst"
    GENERAL_ASSISTANT = "general_assistant"  # Fallback agent

class CostPriority(Enum):
    """Cost optimization priority"""
    COST = "cost"           # Prioritize lowest cost
    PERFORMANCE = "performance"  # Prioritize best performance
    BALANCED = "balanced"   # Balance cost and performance

@dataclass
class AgentConfiguration:
    """Configuration for a specific agent"""
    agent_type: AgentType
    provider_name: str
    model_name: str
    
    # Model parameters
    max_tokens: int = 2000
    temperature: float = 0.7
    top_p: float = 1.0
    
    # Agent behavior
    confidence_threshold: float = 0.85
    specializations: List[str] = field(default_factory=list)
    
    # Fallback configuration
    fallback_provider: Optional[str] = None
    fallback_model: Optional[str] = None
    
    # Cost and performance settings
    cost_priority: CostPriority = CostPriority.BALANCED
    max_cost_per_request: float = 0.10  # USD
    
    # Status
    is_active: bool = True
    last_used: Optional[str] = None
    success_rate: float = 1.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "agent_type": self.agent_type.value,
            "provider_name": self.provider_name,
            "model_name": self.model_name,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "confidence_threshold": self.confidence_threshold,
            "specializations": self.specializations,
            "fallback_provider": self.fallback_provider,
            "fallback_model": self.fallback_model,
            "cost_priority": self.cost_priority.value,
            "max_cost_per_request": self.max_cost_per_request,
            "is_active": self.is_active,
            "last_used": self.last_used,
            "success_rate": self.success_rate
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AgentConfiguration':
        """Create from dictionary"""
        return cls(
            agent_type=AgentType(data["agent_type"]),
            provider_name=data["provider_name"],
            model_name=data["model_name"],
            max_tokens=data.get("max_tokens", 2000),
            temperature=data.get("temperature", 0.7),
            top_p=data.get("top_p", 1.0),
            confidence_threshold=data.get("confidence_threshold", 0.85),
            specializations=data.get("specializations", []),
            fallback_provider=data.get("fallback_provider"),
            fallback_model=data.get("fallback_model"),
            cost_priority=CostPriority(data.get("cost_priority", "balanced")),
            max_cost_per_request=data.get("max_cost_per_request", 0.10),
            is_active=data.get("is_active", True),
            last_used=data.get("last_used"),
            success_rate=data.get("success_rate", 1.0)
        )

# Default agent configurations
DEFAULT_AGENT_CONFIGURATIONS: Dict[AgentType, AgentConfiguration] = {
    AgentType.VISION_SPECIALIST: AgentConfiguration(
        agent_type=AgentType.VISION_SPECIALIST,
        provider_name="openai",
        model_name="gpt-4o",
        max_tokens=2000,
        temperature=0.1,  # Lower temperature for more consistent analysis
        confidence_threshold=0.85,
        specializations=[
            "scene_analysis", 
            "object_detection", 
            "spatial_reasoning",
            "visual_inspection",
            "3d_understanding"
        ],
        fallback_provider="openai",
        fallback_model="gpt-4o-mini",
        cost_priority=CostPriority.PERFORMANCE
    ),
    
    AgentType.CODE_ARCHITECT: AgentConfiguration(
        agent_type=AgentType.CODE_ARCHITECT,
        provider_name="anthropic",
        model_name="claude-3-5-sonnet-20241022",
        max_tokens=4000,
        temperature=0.2,  # Low temperature for consistent code
        confidence_threshold=0.90,
        specializations=[
            "code_generation",
            "refactoring", 
            "optimization",
            "debugging",
            "architecture_design"
        ],
        fallback_provider="openai",
        fallback_model="gpt-4",
        cost_priority=CostPriority.PERFORMANCE
    ),
    
    AgentType.SCENE_OPTIMIZER: AgentConfiguration(
        agent_type=AgentType.SCENE_OPTIMIZER,
        provider_name="openrouter",
        model_name="mistral/mistral-large",
        max_tokens=3000,
        temperature=0.3,
        confidence_threshold=0.80,
        specializations=[
            "performance_analysis",
            "workflow_optimization",
            "scene_optimization",
            "resource_management"
        ],
        fallback_provider="openai",
        fallback_model="gpt-4o-mini",
        cost_priority=CostPriority.BALANCED
    ),
    
    AgentType.UX_ADVISOR: AgentConfiguration(
        agent_type=AgentType.UX_ADVISOR,
        provider_name="openai",
        model_name="gpt-4o-mini",
        max_tokens=2000,
        temperature=0.5,
        confidence_threshold=0.75,
        specializations=[
            "user_experience",
            "interface_design",
            "workflow_improvement",
            "usability_analysis"
        ],
        fallback_provider="anthropic",
        fallback_model="claude-3-haiku-20240307",
        cost_priority=CostPriority.COST
    ),
    
    AgentType.PERFORMANCE_ANALYST: AgentConfiguration(
        agent_type=AgentType.PERFORMANCE_ANALYST,
        provider_name="openai",
        model_name="gpt-4o-mini",
        max_tokens=2000,
        temperature=0.2,
        confidence_threshold=0.85,
        specializations=[
            "performance_monitoring",
            "system_analysis",
            "bottleneck_detection",
            "optimization_recommendations"
        ],
        fallback_provider="openrouter",
        fallback_model="mistral/mistral-large",
        cost_priority=CostPriority.COST
    ),
    
    AgentType.GENERAL_ASSISTANT: AgentConfiguration(
        agent_type=AgentType.GENERAL_ASSISTANT,
        provider_name="openai",
        model_name="gpt-4o-mini",
        max_tokens=1500,
        temperature=0.7,
        confidence_threshold=0.70,
        specializations=[
            "general_assistance",
            "question_answering",
            "basic_tasks"
        ],
        fallback_provider="anthropic",
        fallback_model="claude-3-haiku-20240307",
        cost_priority=CostPriority.COST
    )
}

def get_default_agent_configurations() -> Dict[AgentType, AgentConfiguration]:
    """Get default agent configurations"""
    return DEFAULT_AGENT_CONFIGURATIONS.copy()

def get_agent_configuration(agent_type: AgentType) -> Optional[AgentConfiguration]:
    """Get configuration for specific agent type"""
    return DEFAULT_AGENT_CONFIGURATIONS.get(agent_type)

def get_agent_specializations(agent_type: AgentType) -> List[str]:
    """Get specializations for specific agent type"""
    config = get_agent_configuration(agent_type)
    return config.specializations if config else []

def get_best_agent_for_task(task_keywords: List[str]) -> AgentType:
    """Determine best agent for task based on keywords"""
    # Score each agent based on keyword matches
    agent_scores = {}
    
    for agent_type, config in DEFAULT_AGENT_CONFIGURATIONS.items():
        score = 0
        for keyword in task_keywords:
            for specialization in config.specializations:
                if keyword.lower() in specialization.lower():
                    score += 1
        agent_scores[agent_type] = score
    
    # Return agent with highest score, fallback to general assistant
    if agent_scores:
        best_agent = max(agent_scores.items(), key=lambda x: x[1])
        if best_agent[1] > 0:  # Only return if there's a match
            return best_agent[0]
    
    return AgentType.GENERAL_ASSISTANT

def validate_agent_configuration(config: AgentConfiguration) -> List[str]:
    """Validate agent configuration and return list of errors"""
    errors = []
    
    if not config.provider_name:
        errors.append("Provider name is required")
    
    if not config.model_name:
        errors.append("Model name is required")
    
    if config.max_tokens <= 0:
        errors.append("Max tokens must be positive")
    
    if not 0 <= config.temperature <= 2:
        errors.append("Temperature must be between 0 and 2")
    
    if not 0 <= config.confidence_threshold <= 1:
        errors.append("Confidence threshold must be between 0 and 1")
    
    if config.max_cost_per_request <= 0:
        errors.append("Max cost per request must be positive")
    
    return errors
