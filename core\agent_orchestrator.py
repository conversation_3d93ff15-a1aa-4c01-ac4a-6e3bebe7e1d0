"""
Agent Orchestrator for BlendPro v2.1.0
Manages multi-agent task delegation and coordination
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime

from ..config.agent_configs import AgentType, AgentConfiguration, get_best_agent_for_task
from ..utils.provider_manager import get_provider_manager
from ..utils.dynamic_api_client import get_dynamic_api_client, ProviderResponse
from ..utils.logger import get_logger
from ..config.settings import get_settings

@dataclass
class TaskAnalysis:
    """Analysis of a task for agent selection"""
    task_keywords: List[str]
    complexity_score: float
    estimated_tokens: int
    requires_vision: bool
    requires_code: bool
    best_agent: AgentType
    confidence: float

@dataclass
class AgentResponse:
    """Response from an agent"""
    agent_type: AgentType
    content: str
    confidence: float
    provider_used: str
    model_used: str
    response_time: float
    cost_estimate: float
    success: bool
    error: Optional[str] = None

class AgentOrchestrator:
    """Orchestrates multi-agent task delegation and execution"""
    
    def __init__(self):
        self.logger = get_logger("BlendPro.AgentOrchestrator")
        self.settings = get_settings()
        self.provider_manager = get_provider_manager()
        self.dynamic_client = get_dynamic_api_client()
        
        # Task routing cache
        self._routing_cache: Dict[str, AgentType] = {}
        
        # Agent performance tracking
        self._agent_performance: Dict[AgentType, Dict[str, float]] = {
            agent_type: {"success_rate": 1.0, "avg_response_time": 0.0, "total_requests": 0}
            for agent_type in AgentType
        }
    
    def analyze_task(self, user_input: str, context: Dict[str, Any]) -> TaskAnalysis:
        """Analyze task to determine best agent and requirements"""
        
        # Extract keywords from input
        task_keywords = self._extract_task_keywords(user_input)
        
        # Determine complexity
        complexity_score = self._calculate_complexity(user_input, context)
        
        # Estimate token requirements
        estimated_tokens = min(len(user_input.split()) * 3, 4000)  # Rough estimate
        
        # Check if vision is needed
        requires_vision = self._requires_vision(user_input, context)
        
        # Check if code generation is needed
        requires_code = self._requires_code_generation(user_input)
        
        # Select best agent
        best_agent = get_best_agent_for_task(task_keywords)
        
        # Calculate confidence based on keyword matches
        confidence = self._calculate_agent_confidence(best_agent, task_keywords)
        
        return TaskAnalysis(
            task_keywords=task_keywords,
            complexity_score=complexity_score,
            estimated_tokens=estimated_tokens,
            requires_vision=requires_vision,
            requires_code=requires_code,
            best_agent=best_agent,
            confidence=confidence
        )
    
    def delegate_task(self, user_input: str, context: Dict[str, Any], 
                     force_agent: Optional[AgentType] = None) -> AgentResponse:
        """Delegate task to appropriate agent"""
        
        # Check if multi-agent system is enabled
        if not self.settings.enable_multi_agent_system:
            return self._fallback_to_single_model(user_input, context)
        
        # Analyze task if no agent is forced
        if force_agent:
            selected_agent = force_agent
            confidence = 1.0
        else:
            analysis = self.analyze_task(user_input, context)
            selected_agent = analysis.best_agent
            confidence = analysis.confidence
            
            # Check if confidence meets threshold
            if confidence < self.settings.agent_confidence_threshold:
                self.logger.info(f"Agent confidence {confidence} below threshold {self.settings.agent_confidence_threshold}, using general assistant")
                selected_agent = AgentType.GENERAL_ASSISTANT
        
        # Execute task with selected agent
        return self._execute_with_agent(selected_agent, user_input, context, confidence)
    
    def _execute_with_agent(self, agent_type: AgentType, user_input: str, 
                           context: Dict[str, Any], confidence: float) -> AgentResponse:
        """Execute task with specific agent"""
        
        start_time = datetime.now()
        
        try:
            # Get agent configuration
            agent_config = self.provider_manager.get_agent_config(agent_type)
            if not agent_config:
                return AgentResponse(
                    agent_type=agent_type,
                    content="",
                    confidence=0.0,
                    provider_used="",
                    model_used="",
                    response_time=0.0,
                    cost_estimate=0.0,
                    success=False,
                    error=f"No configuration found for agent {agent_type.value}"
                )
            
            # Prepare messages based on agent specialization
            messages = self._prepare_agent_messages(agent_type, user_input, context)
            
            # Determine if vision is needed
            requires_vision = self._requires_vision(user_input, context)
            
            # Make request through dynamic client
            response = self.dynamic_client.make_request_with_agent(
                agent_type=agent_type,
                messages=messages,
                use_vision=requires_vision
            )
            
            # Calculate response time
            response_time = (datetime.now() - start_time).total_seconds()
            
            # Update agent performance tracking
            self._update_agent_performance(agent_type, response_time, not bool(response.error))
            
            return AgentResponse(
                agent_type=agent_type,
                content=response.content,
                confidence=confidence,
                provider_used=response.provider_name,
                model_used=response.model,
                response_time=response_time,
                cost_estimate=response.cost_estimate,
                success=not bool(response.error),
                error=response.error
            )
            
        except Exception as e:
            self.logger.error(f"Agent execution failed for {agent_type.value}: {e}")
            response_time = (datetime.now() - start_time).total_seconds()
            
            # Update performance tracking for failure
            self._update_agent_performance(agent_type, response_time, False)
            
            return AgentResponse(
                agent_type=agent_type,
                content="",
                confidence=0.0,
                provider_used="",
                model_used="",
                response_time=response_time,
                cost_estimate=0.0,
                success=False,
                error=str(e)
            )
    
    def _prepare_agent_messages(self, agent_type: AgentType, user_input: str, 
                               context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Prepare messages tailored for specific agent"""
        
        # Get agent configuration for specializations
        agent_config = self.provider_manager.get_agent_config(agent_type)
        specializations = agent_config.specializations if agent_config else []
        
        # Base system message
        system_message = f"""You are a specialized AI assistant for Blender 3D software.

Your specializations: {', '.join(specializations)}

Current scene context:
{json.dumps(context, indent=2)}

Provide responses that leverage your specializations. Be precise, helpful, and focused on your area of expertise."""
        
        # Agent-specific system message modifications
        if agent_type == AgentType.VISION_SPECIALIST:
            system_message += "\n\nFocus on visual analysis, scene understanding, and spatial relationships. If provided with images, analyze them thoroughly."
        
        elif agent_type == AgentType.CODE_ARCHITECT:
            system_message += "\n\nGenerate clean, efficient, and well-documented Python code for Blender. Follow best practices and include error handling."
        
        elif agent_type == AgentType.SCENE_OPTIMIZER:
            system_message += "\n\nFocus on performance optimization, workflow efficiency, and resource management. Provide actionable recommendations."
        
        elif agent_type == AgentType.UX_ADVISOR:
            system_message += "\n\nFocus on user experience, interface design, and workflow improvements. Consider usability and accessibility."
        
        elif agent_type == AgentType.PERFORMANCE_ANALYST:
            system_message += "\n\nAnalyze performance metrics, identify bottlenecks, and provide optimization strategies."
        
        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_input}
        ]
    
    def _extract_task_keywords(self, user_input: str) -> List[str]:
        """Extract relevant keywords from user input"""
        # Simple keyword extraction - could be enhanced with NLP
        keywords = []
        
        # Vision-related keywords
        vision_keywords = ["see", "look", "analyze", "visual", "scene", "object", "view", "camera", "render"]
        
        # Code-related keywords
        code_keywords = ["create", "generate", "code", "script", "function", "add", "modify", "delete"]
        
        # Performance-related keywords
        performance_keywords = ["optimize", "performance", "speed", "memory", "efficient", "slow", "lag"]
        
        # UX-related keywords
        ux_keywords = ["interface", "ui", "user", "experience", "workflow", "usability"]
        
        input_lower = user_input.lower()
        
        for keyword in vision_keywords + code_keywords + performance_keywords + ux_keywords:
            if keyword in input_lower:
                keywords.append(keyword)
        
        return keywords
    
    def _calculate_complexity(self, user_input: str, context: Dict[str, Any]) -> float:
        """Calculate task complexity score (0.0 to 1.0)"""
        complexity = 0.0
        
        # Length factor
        complexity += min(len(user_input.split()) / 100, 0.3)
        
        # Multiple objects factor
        selected_objects = context.get('selected_objects', [])
        complexity += min(len(selected_objects) / 10, 0.2)
        
        # Complexity keywords
        complex_keywords = ["complex", "advanced", "multiple", "batch", "all", "entire", "scene"]
        for keyword in complex_keywords:
            if keyword in user_input.lower():
                complexity += 0.1
        
        return min(complexity, 1.0)
    
    def _requires_vision(self, user_input: str, context: Dict[str, Any]) -> bool:
        """Determine if task requires vision capabilities"""
        vision_keywords = ["see", "look", "analyze", "visual", "current", "scene", "what", "this", "these"]
        input_lower = user_input.lower()
        
        return any(keyword in input_lower for keyword in vision_keywords)
    
    def _requires_code_generation(self, user_input: str) -> bool:
        """Determine if task requires code generation"""
        code_keywords = ["create", "generate", "add", "modify", "delete", "script", "code", "function"]
        input_lower = user_input.lower()
        
        return any(keyword in input_lower for keyword in code_keywords)
    
    def _calculate_agent_confidence(self, agent_type: AgentType, keywords: List[str]) -> float:
        """Calculate confidence score for agent selection"""
        agent_config = self.provider_manager.get_agent_config(agent_type)
        if not agent_config:
            return 0.5
        
        # Calculate overlap between keywords and agent specializations
        specializations = [spec.lower() for spec in agent_config.specializations]
        keyword_matches = sum(1 for keyword in keywords if any(keyword in spec for spec in specializations))
        
        if not keywords:
            return 0.7  # Default confidence for general tasks
        
        confidence = keyword_matches / len(keywords)
        
        # Boost confidence based on agent performance history
        performance = self._agent_performance.get(agent_type, {})
        success_rate = performance.get("success_rate", 1.0)
        confidence = confidence * 0.8 + success_rate * 0.2
        
        return min(confidence, 1.0)
    
    def _update_agent_performance(self, agent_type: AgentType, response_time: float, success: bool):
        """Update agent performance tracking"""
        if agent_type not in self._agent_performance:
            self._agent_performance[agent_type] = {
                "success_rate": 1.0,
                "avg_response_time": 0.0,
                "total_requests": 0
            }
        
        perf = self._agent_performance[agent_type]
        perf["total_requests"] += 1
        
        # Update success rate (exponential moving average)
        alpha = 0.1
        perf["success_rate"] = (1 - alpha) * perf["success_rate"] + alpha * (1.0 if success else 0.0)
        
        # Update average response time
        perf["avg_response_time"] = (
            (perf["avg_response_time"] * (perf["total_requests"] - 1) + response_time) / 
            perf["total_requests"]
        )
    
    def _fallback_to_single_model(self, user_input: str, context: Dict[str, Any]) -> AgentResponse:
        """Fallback to single model when multi-agent is disabled"""
        # Use the existing API client for backward compatibility
        from ..utils.api_client import get_api_client, APIRequest
        
        api_client = get_api_client()
        
        messages = [
            {"role": "system", "content": "You are a helpful AI assistant for Blender 3D software."},
            {"role": "user", "content": user_input}
        ]
        
        request = APIRequest(
            messages=messages,
            model=self.settings.default_models.get("general", "gpt-4o-mini"),
            temperature=self.settings.temperature,
            max_tokens=self.settings.max_tokens
        )
        
        start_time = datetime.now()
        response = api_client.make_request(request)
        response_time = (datetime.now() - start_time).total_seconds()
        
        return AgentResponse(
            agent_type=AgentType.GENERAL_ASSISTANT,
            content=response.content,
            confidence=1.0,
            provider_used="legacy",
            model_used=request.model,
            response_time=response_time,
            cost_estimate=0.0,
            success=not bool(response.error),
            error=response.error
        )
    
    def get_agent_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary for all agents"""
        return {
            "agent_performance": self._agent_performance.copy(),
            "total_cached_routes": len(self._routing_cache)
        }

# Global instance
_agent_orchestrator = None

def get_agent_orchestrator() -> AgentOrchestrator:
    """Get global agent orchestrator instance"""
    global _agent_orchestrator
    if _agent_orchestrator is None:
        _agent_orchestrator = AgentOrchestrator()
    return _agent_orchestrator
