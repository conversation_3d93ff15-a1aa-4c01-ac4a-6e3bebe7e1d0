"""
Depth Analyzer for BlendPro v2.1.0
Advanced depth buffer extraction and analysis for 3D scene understanding
"""

import bpy
import gpu
import time
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import mathutils

from ..config.settings import get_settings
from ..utils.logger import get_logger

# Check for numpy availability
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    np = None

@dataclass
class DepthPoint:
    """Represents a point in 3D space with depth information"""
    x: float
    y: float
    z: float
    depth: float
    world_position: mathutils.Vector
    screen_position: Tuple[int, int]

@dataclass
class DepthRegion:
    """Represents a region with similar depth values"""
    min_depth: float
    max_depth: float
    avg_depth: float
    pixel_count: int
    bounding_box: Tuple[int, int, int, int]  # x, y, width, height
    center: Tuple[int, int]

@dataclass
class DepthAnalysisResult:
    """Result of depth analysis"""
    depth_map: Optional[np.ndarray]
    depth_statistics: Dict[str, float]
    depth_regions: List[DepthRegion]
    depth_points: List[DepthPoint]
    near_plane: float
    far_plane: float
    resolution: Tuple[int, int]
    analysis_time: float
    success: bool
    error: Optional[str] = None

class DepthAnalyzer:
    """Analyzes depth information from Blender viewport"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger("BlendPro.DepthAnalyzer")
        self._depth_cache: Dict[str, Tuple[DepthAnalysisResult, float]] = {}
        self._cache_timeout = 10.0  # seconds
    
    def analyze_depth(
        self, 
        context, 
        resolution: Optional[Tuple[int, int]] = None,
        use_cache: bool = True,
        sample_density: float = 1.0
    ) -> DepthAnalysisResult:
        """Analyze depth information from current viewport"""
        
        start_time = time.time()
        
        # Generate cache key
        cache_key = self._generate_cache_key(context, resolution, sample_density)
        
        # Check cache
        if use_cache and cache_key in self._depth_cache:
            cached_result, timestamp = self._depth_cache[cache_key]
            if time.time() - timestamp < self._cache_timeout:
                self.logger.debug("Using cached depth analysis")
                return cached_result
        
        try:
            # Extract depth buffer
            depth_data = self._extract_depth_buffer(context, resolution)
            
            if not depth_data["success"]:
                return DepthAnalysisResult(
                    depth_map=None,
                    depth_statistics={},
                    depth_regions=[],
                    depth_points=[],
                    near_plane=0.0,
                    far_plane=0.0,
                    resolution=(0, 0),
                    analysis_time=time.time() - start_time,
                    success=False,
                    error=depth_data.get("error", "Unknown error")
                )
            
            # Analyze depth map
            depth_map = depth_data["depth_map"]
            near_plane = depth_data["near_plane"]
            far_plane = depth_data["far_plane"]
            resolution = depth_data["resolution"]
            
            # Calculate depth statistics
            depth_stats = self._calculate_depth_statistics(depth_map, near_plane, far_plane)
            
            # Find depth regions
            depth_regions = self._find_depth_regions(depth_map, near_plane, far_plane)
            
            # Sample depth points
            depth_points = self._sample_depth_points(
                context, depth_map, near_plane, far_plane, sample_density
            )
            
            result = DepthAnalysisResult(
                depth_map=depth_map,
                depth_statistics=depth_stats,
                depth_regions=depth_regions,
                depth_points=depth_points,
                near_plane=near_plane,
                far_plane=far_plane,
                resolution=resolution,
                analysis_time=time.time() - start_time,
                success=True
            )
            
            # Cache result
            if use_cache:
                self._depth_cache[cache_key] = (result, time.time())
            
            return result
            
        except Exception as e:
            self.logger.error(f"Depth analysis failed: {e}")
            return DepthAnalysisResult(
                depth_map=None,
                depth_statistics={},
                depth_regions=[],
                depth_points=[],
                near_plane=0.0,
                far_plane=0.0,
                resolution=(0, 0),
                analysis_time=time.time() - start_time,
                success=False,
                error=str(e)
            )
    
    def _extract_depth_buffer(
        self, 
        context, 
        resolution: Optional[Tuple[int, int]] = None
    ) -> Dict[str, Any]:
        """Extract depth buffer from current viewport"""
        
        if not NUMPY_AVAILABLE:
            return {"success": False, "error": "NumPy not available for depth analysis"}
        
        try:
            # Get viewport area
            area = context.area
            if not area or area.type != 'VIEW_3D':
                return {"success": False, "error": "No 3D viewport available"}
            
            space = area.spaces.active
            if not space or space.type != 'VIEW_3D':
                return {"success": False, "error": "Invalid 3D viewport"}
            
            # Get resolution
            if resolution:
                width, height = resolution
            else:
                width, height = area.width, area.height
            
            # Ensure minimum resolution
            width = max(width, 64)
            height = max(height, 64)
            
            # Get camera parameters
            region_3d = space.region_3d
            if not region_3d:
                return {"success": False, "error": "No 3D region available"}
            
            near_plane = space.clip_start
            far_plane = space.clip_end
            
            # Create offscreen buffer
            offscreen = gpu.types.GPUOffScreen(width, height)
            
            with offscreen.bind():
                # Clear depth buffer
                gpu.state.depth_test_set('LESS')
                gpu.state.depth_mask_set(True)
                
                # Get view and projection matrices
                view_matrix = region_3d.view_matrix
                projection_matrix = region_3d.window_matrix
                
                # Render scene (simplified - just clear for now)
                # In a full implementation, we would render the actual scene
                
                # Read depth buffer
                depth_buffer = gpu.types.Buffer('FLOAT', width * height)
                offscreen.read_depth(0, 0, width, height, depth_buffer)
            
            # Convert buffer to numpy array
            depth_array = np.frombuffer(depth_buffer, dtype=np.float32)
            depth_map = depth_array.reshape((height, width))
            
            # Flip vertically to match screen coordinates
            depth_map = np.flipud(depth_map)
            
            return {
                "success": True,
                "depth_map": depth_map,
                "near_plane": near_plane,
                "far_plane": far_plane,
                "resolution": (width, height),
                "view_matrix": view_matrix,
                "projection_matrix": projection_matrix
            }
            
        except Exception as e:
            return {"success": False, "error": f"Depth buffer extraction failed: {str(e)}"}
    
    def _calculate_depth_statistics(
        self, 
        depth_map: np.ndarray, 
        near_plane: float, 
        far_plane: float
    ) -> Dict[str, float]:
        """Calculate statistical information about depth map"""
        
        if not NUMPY_AVAILABLE or depth_map is None:
            return {}
        
        # Convert normalized depth to world space depth
        # Depth buffer values are typically in [0, 1] range
        world_depth = near_plane + (far_plane - near_plane) * depth_map
        
        # Filter out background/invalid depths (typically 1.0)
        valid_depths = world_depth[depth_map < 0.999]
        
        if len(valid_depths) == 0:
            return {
                "min_depth": near_plane,
                "max_depth": far_plane,
                "mean_depth": (near_plane + far_plane) / 2,
                "median_depth": (near_plane + far_plane) / 2,
                "std_depth": 0.0,
                "valid_pixel_ratio": 0.0
            }
        
        return {
            "min_depth": float(np.min(valid_depths)),
            "max_depth": float(np.max(valid_depths)),
            "mean_depth": float(np.mean(valid_depths)),
            "median_depth": float(np.median(valid_depths)),
            "std_depth": float(np.std(valid_depths)),
            "valid_pixel_ratio": len(valid_depths) / depth_map.size
        }
    
    def _find_depth_regions(
        self, 
        depth_map: np.ndarray, 
        near_plane: float, 
        far_plane: float,
        num_regions: int = 5
    ) -> List[DepthRegion]:
        """Find regions with similar depth values"""
        
        if not NUMPY_AVAILABLE or depth_map is None:
            return []
        
        regions = []
        
        try:
            # Convert to world space depth
            world_depth = near_plane + (far_plane - near_plane) * depth_map
            
            # Create depth bins
            valid_mask = depth_map < 0.999
            if not np.any(valid_mask):
                return regions
            
            valid_depths = world_depth[valid_mask]
            min_depth = np.min(valid_depths)
            max_depth = np.max(valid_depths)
            
            # Create depth bins
            depth_bins = np.linspace(min_depth, max_depth, num_regions + 1)
            
            for i in range(num_regions):
                bin_min = depth_bins[i]
                bin_max = depth_bins[i + 1]
                
                # Find pixels in this depth range
                mask = (world_depth >= bin_min) & (world_depth < bin_max) & valid_mask
                
                if np.any(mask):
                    # Find bounding box
                    y_coords, x_coords = np.where(mask)
                    
                    if len(x_coords) > 0 and len(y_coords) > 0:
                        min_x, max_x = int(np.min(x_coords)), int(np.max(x_coords))
                        min_y, max_y = int(np.min(y_coords)), int(np.max(y_coords))
                        
                        region = DepthRegion(
                            min_depth=bin_min,
                            max_depth=bin_max,
                            avg_depth=float(np.mean(world_depth[mask])),
                            pixel_count=int(np.sum(mask)),
                            bounding_box=(min_x, min_y, max_x - min_x, max_y - min_y),
                            center=(int((min_x + max_x) / 2), int((min_y + max_y) / 2))
                        )
                        regions.append(region)
            
        except Exception as e:
            self.logger.error(f"Depth region analysis failed: {e}")

        return regions

    def _sample_depth_points(
        self,
        context,
        depth_map: np.ndarray,
        near_plane: float,
        far_plane: float,
        sample_density: float = 1.0
    ) -> List[DepthPoint]:
        """Sample depth points from the depth map"""

        if not NUMPY_AVAILABLE or depth_map is None:
            return []

        points = []

        try:
            height, width = depth_map.shape

            # Calculate sampling step based on density
            step = max(1, int(1.0 / sample_density))

            # Get viewport matrices for world space conversion
            area = context.area
            space = area.spaces.active
            region_3d = space.region_3d

            view_matrix = region_3d.view_matrix
            projection_matrix = region_3d.window_matrix

            # Sample points
            for y in range(0, height, step):
                for x in range(0, width, step):
                    depth_value = depth_map[y, x]

                    # Skip background pixels
                    if depth_value >= 0.999:
                        continue

                    # Convert to world space depth
                    world_depth = near_plane + (far_plane - near_plane) * depth_value

                    # Convert screen coordinates to world coordinates
                    world_pos = self._screen_to_world(
                        x, y, depth_value, width, height,
                        view_matrix, projection_matrix
                    )

                    if world_pos:
                        point = DepthPoint(
                            x=world_pos.x,
                            y=world_pos.y,
                            z=world_pos.z,
                            depth=world_depth,
                            world_position=world_pos,
                            screen_position=(x, y)
                        )
                        points.append(point)

        except Exception as e:
            self.logger.error(f"Depth point sampling failed: {e}")

        return points

    def _screen_to_world(
        self,
        screen_x: int,
        screen_y: int,
        depth: float,
        width: int,
        height: int,
        view_matrix: mathutils.Matrix,
        projection_matrix: mathutils.Matrix
    ) -> Optional[mathutils.Vector]:
        """Convert screen coordinates to world coordinates"""

        try:
            # Normalize screen coordinates to [-1, 1]
            ndc_x = (2.0 * screen_x / width) - 1.0
            ndc_y = (2.0 * screen_y / height) - 1.0
            ndc_z = 2.0 * depth - 1.0  # Convert [0,1] to [-1,1]

            # Create NDC point
            ndc_point = mathutils.Vector((ndc_x, ndc_y, ndc_z, 1.0))

            # Convert to world space
            # First, inverse projection
            inv_projection = projection_matrix.inverted()
            view_space = inv_projection @ ndc_point

            # Perspective divide
            if view_space.w != 0:
                view_space /= view_space.w

            # Then, inverse view
            inv_view = view_matrix.inverted()
            world_space = inv_view @ view_space

            return mathutils.Vector((world_space.x, world_space.y, world_space.z))

        except Exception as e:
            self.logger.error(f"Screen to world conversion failed: {e}")
            return None

    def _generate_cache_key(
        self,
        context,
        resolution: Optional[Tuple[int, int]],
        sample_density: float
    ) -> str:
        """Generate cache key for depth analysis"""

        try:
            area = context.area
            space = context.space_data

            cache_components = []

            if area and area.type == 'VIEW_3D' and space:
                # Add viewport-specific information
                cache_components.extend([
                    str(resolution or (area.width, area.height)),
                    f"density_{sample_density:.2f}",
                    str(space.shading.type)
                ])

                # Add view matrix components
                if hasattr(space, 'region_3d') and space.region_3d:
                    view_location = space.region_3d.view_location
                    view_rotation = space.region_3d.view_rotation
                    view_distance = space.region_3d.view_distance

                    cache_components.extend([
                        f"{view_location.x:.2f}_{view_location.y:.2f}_{view_location.z:.2f}",
                        f"{view_rotation.w:.2f}_{view_rotation.x:.2f}_{view_rotation.y:.2f}_{view_rotation.z:.2f}",
                        f"{view_distance:.2f}"
                    ])

            return "_".join(cache_components)

        except Exception:
            return f"fallback_{time.time()}"

    def get_depth_at_pixel(
        self,
        context,
        pixel_x: int,
        pixel_y: int
    ) -> Optional[float]:
        """Get depth value at specific pixel coordinates"""

        try:
            # Quick depth analysis for single pixel
            result = self.analyze_depth(context, use_cache=True)

            if result.success and result.depth_map is not None:
                height, width = result.depth_map.shape

                # Check bounds
                if 0 <= pixel_x < width and 0 <= pixel_y < height:
                    depth_value = result.depth_map[pixel_y, pixel_x]

                    # Convert to world space depth
                    world_depth = result.near_plane + (result.far_plane - result.near_plane) * depth_value
                    return world_depth

        except Exception as e:
            self.logger.error(f"Pixel depth query failed: {e}")

        return None

    def get_depth_statistics_summary(self, result: DepthAnalysisResult) -> str:
        """Get human-readable summary of depth statistics"""

        if not result.success or not result.depth_statistics:
            return "Depth analysis failed or no data available"

        stats = result.depth_statistics

        summary = f"""Depth Analysis Summary:
- Scene depth range: {stats.get('min_depth', 0):.2f} to {stats.get('max_depth', 0):.2f} units
- Average depth: {stats.get('mean_depth', 0):.2f} units
- Depth variation: {stats.get('std_depth', 0):.2f} units
- Valid pixels: {stats.get('valid_pixel_ratio', 0)*100:.1f}%
- Depth regions found: {len(result.depth_regions)}
- Sample points: {len(result.depth_points)}
- Analysis time: {result.analysis_time:.3f} seconds"""

        return summary

    def clear_cache(self):
        """Clear depth analysis cache"""
        self._depth_cache.clear()
        self.logger.debug("Depth analysis cache cleared")

# Global instance
_depth_analyzer = None

def get_depth_analyzer() -> DepthAnalyzer:
    """Get global depth analyzer instance"""
    global _depth_analyzer
    if _depth_analyzer is None:
        _depth_analyzer = DepthAnalyzer()
    return _depth_analyzer
