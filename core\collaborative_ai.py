"""
Collaborative AI System for BlendPro v2.1.0
Multi-model consensus, confidence aggregation, and disagreement resolution
"""

import asyncio
import time
import statistics
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from ..config.settings import get_settings
from ..config.agent_configs import AgentType, AgentConfiguration
from ..utils.provider_manager import get_provider_manager
from ..utils.dynamic_api_client import get_dynamic_api_client, ProviderResponse
from ..utils.logger import get_logger

class ConsensusMethod(Enum):
    """Methods for reaching consensus between models"""
    MAJORITY_VOTE = "majority_vote"
    WEIGHTED_AVERAGE = "weighted_average"
    CONFIDENCE_BASED = "confidence_based"
    EXPERT_SELECTION = "expert_selection"
    HYBRID = "hybrid"

class DisagreementResolution(Enum):
    """Methods for resolving disagreements"""
    HIGHEST_CONFIDENCE = "highest_confidence"
    MAJORITY_RULE = "majority_rule"
    EXPERT_ARBITRATION = "expert_arbitration"
    WEIGHTED_CONSENSUS = "weighted_consensus"
    HUMAN_REVIEW = "human_review"

@dataclass
class ModelResponse:
    """Response from a single model"""
    model_id: str
    provider_name: str
    model_name: str
    content: str
    confidence: float
    response_time: float
    cost_estimate: float
    metadata: Dict[str, Any]
    success: bool
    error: Optional[str] = None

@dataclass
class ConsensusResult:
    """Result of consensus analysis"""
    final_response: str
    consensus_confidence: float
    individual_responses: List[ModelResponse]
    consensus_method: ConsensusMethod
    disagreement_detected: bool
    disagreement_resolution: Optional[DisagreementResolution]
    total_cost: float
    total_time: float
    model_weights: Dict[str, float]
    success: bool
    error: Optional[str] = None

@dataclass
class CollaborativeConfig:
    """Configuration for collaborative analysis"""
    models: List[Tuple[str, str]]  # (provider_name, model_name) pairs
    consensus_method: ConsensusMethod = ConsensusMethod.CONFIDENCE_BASED
    disagreement_resolution: DisagreementResolution = DisagreementResolution.HIGHEST_CONFIDENCE
    confidence_threshold: float = 0.75
    disagreement_threshold: float = 0.3
    max_cost_per_analysis: float = 1.0
    timeout_per_model: float = 30.0
    enable_parallel_execution: bool = True
    model_weights: Optional[Dict[str, float]] = None

class CollaborativeAI:
    """Multi-model collaborative AI system"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger("BlendPro.CollaborativeAI")
        self.provider_manager = get_provider_manager()
        self.dynamic_client = get_dynamic_api_client()
        
        # Performance tracking
        self._collaboration_history: List[ConsensusResult] = []
        self._model_performance: Dict[str, Dict[str, float]] = {}
    
    def analyze_collaboratively(
        self, 
        messages: List[Dict[str, Any]], 
        config: CollaborativeConfig,
        context: Optional[Dict[str, Any]] = None
    ) -> ConsensusResult:
        """Perform collaborative analysis using multiple models"""
        
        start_time = time.time()
        
        try:
            # Validate configuration
            if not self._validate_config(config):
                return ConsensusResult(
                    final_response="",
                    consensus_confidence=0.0,
                    individual_responses=[],
                    consensus_method=config.consensus_method,
                    disagreement_detected=False,
                    disagreement_resolution=None,
                    total_cost=0.0,
                    total_time=time.time() - start_time,
                    model_weights={},
                    success=False,
                    error="Invalid configuration"
                )
            
            # Get responses from all models
            individual_responses = self._get_model_responses(messages, config)
            
            if not individual_responses:
                return ConsensusResult(
                    final_response="",
                    consensus_confidence=0.0,
                    individual_responses=[],
                    consensus_method=config.consensus_method,
                    disagreement_detected=False,
                    disagreement_resolution=None,
                    total_cost=0.0,
                    total_time=time.time() - start_time,
                    model_weights={},
                    success=False,
                    error="No successful model responses"
                )
            
            # Calculate costs
            total_cost = sum(response.cost_estimate for response in individual_responses)
            
            # Check cost limit
            if total_cost > config.max_cost_per_analysis:
                self.logger.warning(f"Collaborative analysis cost ({total_cost:.4f}) exceeds limit ({config.max_cost_per_analysis:.4f})")
            
            # Detect disagreements
            disagreement_detected = self._detect_disagreement(individual_responses, config.disagreement_threshold)
            
            # Apply consensus method
            final_response, consensus_confidence, model_weights = self._apply_consensus(
                individual_responses, config
            )
            
            # Apply disagreement resolution if needed
            disagreement_resolution = None
            if disagreement_detected:
                final_response, consensus_confidence, disagreement_resolution = self._resolve_disagreement(
                    individual_responses, config, final_response, consensus_confidence
                )
            
            # Update performance tracking
            self._update_model_performance(individual_responses)
            
            result = ConsensusResult(
                final_response=final_response,
                consensus_confidence=consensus_confidence,
                individual_responses=individual_responses,
                consensus_method=config.consensus_method,
                disagreement_detected=disagreement_detected,
                disagreement_resolution=disagreement_resolution,
                total_cost=total_cost,
                total_time=time.time() - start_time,
                model_weights=model_weights,
                success=True
            )
            
            # Store in history
            self._collaboration_history.append(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Collaborative analysis failed: {e}")
            return ConsensusResult(
                final_response="",
                consensus_confidence=0.0,
                individual_responses=[],
                consensus_method=config.consensus_method,
                disagreement_detected=False,
                disagreement_resolution=None,
                total_cost=0.0,
                total_time=time.time() - start_time,
                model_weights={},
                success=False,
                error=str(e)
            )
    
    def _validate_config(self, config: CollaborativeConfig) -> bool:
        """Validate collaborative configuration"""
        
        if not config.models:
            return False
        
        if len(config.models) < 2:
            self.logger.warning("Collaborative analysis requires at least 2 models")
            return False
        
        # Check if models are available
        for provider_name, model_name in config.models:
            provider = self.provider_manager.get_provider(provider_name)
            if not provider:
                self.logger.error(f"Provider '{provider_name}' not found")
                return False
            
            if not provider.is_active:
                self.logger.error(f"Provider '{provider_name}' is not active")
                return False
            
            if model_name not in provider.supported_models:
                self.logger.error(f"Model '{model_name}' not supported by provider '{provider_name}'")
                return False
        
        return True
    
    def _get_model_responses(
        self, 
        messages: List[Dict[str, Any]], 
        config: CollaborativeConfig
    ) -> List[ModelResponse]:
        """Get responses from all configured models"""
        
        responses = []
        
        if config.enable_parallel_execution:
            # Parallel execution
            with ThreadPoolExecutor(max_workers=len(config.models)) as executor:
                future_to_model = {
                    executor.submit(self._get_single_model_response, messages, provider_name, model_name, config): 
                    (provider_name, model_name)
                    for provider_name, model_name in config.models
                }
                
                for future in as_completed(future_to_model):
                    provider_name, model_name = future_to_model[future]
                    try:
                        response = future.result(timeout=config.timeout_per_model)
                        if response:
                            responses.append(response)
                    except Exception as e:
                        self.logger.error(f"Model {provider_name}/{model_name} failed: {e}")
        else:
            # Sequential execution
            for provider_name, model_name in config.models:
                response = self._get_single_model_response(messages, provider_name, model_name, config)
                if response:
                    responses.append(response)
        
        return responses

    def _get_single_model_response(
        self,
        messages: List[Dict[str, Any]],
        provider_name: str,
        model_name: str,
        config: CollaborativeConfig
    ) -> Optional[ModelResponse]:
        """Get response from a single model"""

        start_time = time.time()

        try:
            from ..utils.dynamic_api_client import ProviderRequest

            # Create request
            request = ProviderRequest(
                provider_name=provider_name,
                model_name=model_name,
                messages=messages,
                temperature=0.7,  # Standard temperature for consistency
                max_tokens=2000,
                timeout=config.timeout_per_model
            )

            # Make request
            response = self.dynamic_client.make_request_with_provider(request)

            if response.error:
                return None

            # Calculate confidence (simplified heuristic)
            confidence = self._calculate_response_confidence(response.content, response)

            return ModelResponse(
                model_id=f"{provider_name}/{model_name}",
                provider_name=provider_name,
                model_name=model_name,
                content=response.content,
                confidence=confidence,
                response_time=time.time() - start_time,
                cost_estimate=response.cost_estimate,
                metadata={
                    "usage": response.usage,
                    "finish_reason": response.finish_reason
                },
                success=True
            )

        except Exception as e:
            self.logger.error(f"Single model response failed for {provider_name}/{model_name}: {e}")
            return None

    def _calculate_response_confidence(self, content: str, response: ProviderResponse) -> float:
        """Calculate confidence score for a response"""

        confidence = 0.5  # Base confidence

        # Length factor (longer responses might be more detailed)
        if len(content) > 100:
            confidence += 0.1
        if len(content) > 500:
            confidence += 0.1

        # Certainty indicators
        certainty_words = ["definitely", "certainly", "clearly", "obviously", "precisely"]
        uncertainty_words = ["maybe", "perhaps", "possibly", "might", "could"]

        certainty_count = sum(1 for word in certainty_words if word in content.lower())
        uncertainty_count = sum(1 for word in uncertainty_words if word in content.lower())

        confidence += certainty_count * 0.05
        confidence -= uncertainty_count * 0.05

        # Response quality indicators
        if "```" in content:  # Code blocks
            confidence += 0.1
        if content.count('\n') > 3:  # Structured response
            confidence += 0.05

        return max(0.0, min(1.0, confidence))

    def _detect_disagreement(self, responses: List[ModelResponse], threshold: float) -> bool:
        """Detect if there's significant disagreement between models"""

        if len(responses) < 2:
            return False

        # Simple disagreement detection based on confidence variance
        confidences = [r.confidence for r in responses]
        confidence_variance = statistics.variance(confidences) if len(confidences) > 1 else 0

        # Content similarity check (simplified)
        content_similarities = []
        for i in range(len(responses)):
            for j in range(i + 1, len(responses)):
                similarity = self._calculate_content_similarity(responses[i].content, responses[j].content)
                content_similarities.append(similarity)

        avg_similarity = statistics.mean(content_similarities) if content_similarities else 1.0

        # Disagreement if low similarity or high confidence variance
        disagreement_score = (1 - avg_similarity) + confidence_variance

        return disagreement_score > threshold

    def _calculate_content_similarity(self, content1: str, content2: str) -> float:
        """Calculate similarity between two content strings"""

        # Simple word-based similarity
        words1 = set(content1.lower().split())
        words2 = set(content2.lower().split())

        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0

    def _apply_consensus(
        self,
        responses: List[ModelResponse],
        config: CollaborativeConfig
    ) -> Tuple[str, float, Dict[str, float]]:
        """Apply consensus method to combine responses"""

        if not responses:
            return "", 0.0, {}

        if len(responses) == 1:
            return responses[0].content, responses[0].confidence, {responses[0].model_id: 1.0}

        model_weights = config.model_weights or {}

        if config.consensus_method == ConsensusMethod.HIGHEST_CONFIDENCE:
            best_response = max(responses, key=lambda r: r.confidence)
            return best_response.content, best_response.confidence, {best_response.model_id: 1.0}

        elif config.consensus_method == ConsensusMethod.WEIGHTED_AVERAGE:
            return self._weighted_average_consensus(responses, model_weights)

        elif config.consensus_method == ConsensusMethod.CONFIDENCE_BASED:
            return self._confidence_based_consensus(responses)

        elif config.consensus_method == ConsensusMethod.MAJORITY_VOTE:
            return self._majority_vote_consensus(responses)

        else:  # Default to confidence-based
            return self._confidence_based_consensus(responses)

    def _confidence_based_consensus(self, responses: List[ModelResponse]) -> Tuple[str, float, Dict[str, float]]:
        """Confidence-based consensus"""

        # Weight responses by confidence
        total_confidence = sum(r.confidence for r in responses)

        if total_confidence == 0:
            # Fallback to first response
            return responses[0].content, 0.0, {responses[0].model_id: 1.0}

        weights = {r.model_id: r.confidence / total_confidence for r in responses}

        # For now, return the highest confidence response
        # In a full implementation, we would combine content intelligently
        best_response = max(responses, key=lambda r: r.confidence)

        # Calculate consensus confidence as weighted average
        consensus_confidence = sum(r.confidence * weights[r.model_id] for r in responses)

        return best_response.content, consensus_confidence, weights

    def _weighted_average_consensus(
        self,
        responses: List[ModelResponse],
        model_weights: Dict[str, float]
    ) -> Tuple[str, float, Dict[str, float]]:
        """Weighted average consensus"""

        # Normalize weights
        total_weight = sum(model_weights.get(r.model_id, 1.0) for r in responses)
        normalized_weights = {
            r.model_id: model_weights.get(r.model_id, 1.0) / total_weight
            for r in responses
        }

        # For now, return the response with highest weight
        # In a full implementation, we would combine content intelligently
        best_response = max(responses, key=lambda r: normalized_weights[r.model_id])

        # Calculate consensus confidence
        consensus_confidence = sum(
            r.confidence * normalized_weights[r.model_id] for r in responses
        )

        return best_response.content, consensus_confidence, normalized_weights

    def _majority_vote_consensus(self, responses: List[ModelResponse]) -> Tuple[str, float, Dict[str, float]]:
        """Majority vote consensus"""

        # Simple majority vote - group similar responses
        response_groups = {}

        for response in responses:
            # Find similar responses
            found_group = False
            for group_key, group_responses in response_groups.items():
                if self._calculate_content_similarity(response.content, group_responses[0].content) > 0.7:
                    group_responses.append(response)
                    found_group = True
                    break

            if not found_group:
                response_groups[response.model_id] = [response]

        # Find majority group
        majority_group = max(response_groups.values(), key=len)

        # Return best response from majority group
        best_response = max(majority_group, key=lambda r: r.confidence)

        # Calculate weights
        weights = {r.model_id: 1.0 / len(responses) for r in responses}

        # Calculate consensus confidence
        consensus_confidence = statistics.mean([r.confidence for r in majority_group])

        return best_response.content, consensus_confidence, weights

    def _resolve_disagreement(
        self,
        responses: List[ModelResponse],
        config: CollaborativeConfig,
        current_response: str,
        current_confidence: float
    ) -> Tuple[str, float, DisagreementResolution]:
        """Resolve disagreement between models"""

        if config.disagreement_resolution == DisagreementResolution.HIGHEST_CONFIDENCE:
            best_response = max(responses, key=lambda r: r.confidence)
            return best_response.content, best_response.confidence, DisagreementResolution.HIGHEST_CONFIDENCE

        elif config.disagreement_resolution == DisagreementResolution.EXPERT_ARBITRATION:
            # Use the most expensive/advanced model as expert
            expert_response = max(responses, key=lambda r: r.cost_estimate)
            return expert_response.content, expert_response.confidence, DisagreementResolution.EXPERT_ARBITRATION

        elif config.disagreement_resolution == DisagreementResolution.WEIGHTED_CONSENSUS:
            # Re-apply weighted consensus with disagreement penalty
            weighted_response, weighted_confidence, _ = self._confidence_based_consensus(responses)
            # Apply penalty for disagreement
            adjusted_confidence = weighted_confidence * 0.8
            return weighted_response, adjusted_confidence, DisagreementResolution.WEIGHTED_CONSENSUS

        else:  # Default to current response
            return current_response, current_confidence, config.disagreement_resolution

    def _update_model_performance(self, responses: List[ModelResponse]):
        """Update model performance tracking"""

        for response in responses:
            model_id = response.model_id

            if model_id not in self._model_performance:
                self._model_performance[model_id] = {
                    "total_requests": 0,
                    "success_rate": 1.0,
                    "avg_confidence": 0.0,
                    "avg_response_time": 0.0,
                    "total_cost": 0.0
                }

            perf = self._model_performance[model_id]
            perf["total_requests"] += 1

            # Update averages
            alpha = 0.1  # Learning rate
            perf["avg_confidence"] = (1 - alpha) * perf["avg_confidence"] + alpha * response.confidence
            perf["avg_response_time"] = (1 - alpha) * perf["avg_response_time"] + alpha * response.response_time
            perf["total_cost"] += response.cost_estimate

    def get_collaboration_summary(self) -> Dict[str, Any]:
        """Get summary of collaboration performance"""

        if not self._collaboration_history:
            return {"message": "No collaboration history available"}

        total_collaborations = len(self._collaboration_history)
        successful_collaborations = sum(1 for r in self._collaboration_history if r.success)

        avg_confidence = statistics.mean([r.consensus_confidence for r in self._collaboration_history if r.success])
        avg_cost = statistics.mean([r.total_cost for r in self._collaboration_history if r.success])
        avg_time = statistics.mean([r.total_time for r in self._collaboration_history if r.success])

        disagreement_rate = sum(1 for r in self._collaboration_history if r.disagreement_detected) / total_collaborations

        return {
            "total_collaborations": total_collaborations,
            "success_rate": successful_collaborations / total_collaborations,
            "avg_consensus_confidence": avg_confidence,
            "avg_cost_per_collaboration": avg_cost,
            "avg_time_per_collaboration": avg_time,
            "disagreement_rate": disagreement_rate,
            "model_performance": self._model_performance
        }

    def get_optimal_model_combination(self, max_cost: float = 0.5) -> List[Tuple[str, str]]:
        """Get optimal model combination based on performance and cost"""

        if not self._model_performance:
            # Return default combination
            return [("openai", "gpt-4o-mini"), ("anthropic", "claude-3-haiku-20240307")]

        # Score models based on performance and cost
        model_scores = {}

        for model_id, perf in self._model_performance.items():
            if perf["total_requests"] < 3:  # Skip models with insufficient data
                continue

            # Calculate score (higher is better)
            confidence_score = perf["avg_confidence"]
            speed_score = 1.0 / (1.0 + perf["avg_response_time"])  # Faster is better
            cost_efficiency = perf["avg_confidence"] / (perf["total_cost"] / perf["total_requests"] + 0.001)

            total_score = confidence_score * 0.4 + speed_score * 0.3 + cost_efficiency * 0.3
            model_scores[model_id] = total_score

        # Select top models within cost limit
        sorted_models = sorted(model_scores.items(), key=lambda x: x[1], reverse=True)

        selected_models = []
        estimated_cost = 0.0

        for model_id, score in sorted_models:
            model_cost = self._model_performance[model_id]["total_cost"] / self._model_performance[model_id]["total_requests"]

            if estimated_cost + model_cost <= max_cost:
                provider_name, model_name = model_id.split("/", 1)
                selected_models.append((provider_name, model_name))
                estimated_cost += model_cost

                if len(selected_models) >= 3:  # Limit to 3 models
                    break

        return selected_models or [("openai", "gpt-4o-mini"), ("anthropic", "claude-3-haiku-20240307")]

    def clear_history(self):
        """Clear collaboration history and performance data"""
        self._collaboration_history.clear()
        self._model_performance.clear()

# Global instance
_collaborative_ai = None

def get_collaborative_ai() -> CollaborativeAI:
    """Get global collaborative AI instance"""
    global _collaborative_ai
    if _collaborative_ai is None:
        _collaborative_ai = CollaborativeAI()
    return _collaborative_ai
