"""
AI Provider Management for BlendPro v2.1.0
Defines provider specifications and registry for multi-provider support
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import json
import os

class ProviderType(Enum):
    """Provider type enumeration"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    MISTRAL = "mistral"
    OPENROUTER = "openrouter"
    CUSTOM = "custom"

class AuthenticationType(Enum):
    """Authentication type enumeration"""
    API_KEY = "api_key"
    BEARER_TOKEN = "bearer"
    OAUTH = "oauth"
    CUSTOM_HEADER = "custom_header"

@dataclass
class RateLimit:
    """Rate limiting configuration"""
    requests_per_minute: int = 60
    tokens_per_minute: int = 100000
    concurrent_requests: int = 10

@dataclass
class CostConfig:
    """Cost configuration for provider"""
    input_cost_per_1k_tokens: float = 0.0
    output_cost_per_1k_tokens: float = 0.0
    image_cost_per_request: float = 0.0
    currency: str = "USD"

@dataclass
class AIProvider:
    """AI Provider specification"""
    name: str
    display_name: str
    provider_type: ProviderType
    api_endpoint: str
    api_key: str = ""
    
    # Authentication
    authentication_type: AuthenticationType = AuthenticationType.API_KEY
    custom_headers: Dict[str, str] = field(default_factory=dict)
    
    # Capabilities
    supported_models: List[str] = field(default_factory=list)
    capabilities: List[str] = field(default_factory=list)
    supports_vision: bool = False
    supports_function_calling: bool = False
    supports_streaming: bool = True
    
    # Rate limiting and costs
    rate_limits: RateLimit = field(default_factory=RateLimit)
    cost_config: CostConfig = field(default_factory=CostConfig)
    
    # Configuration
    default_model: str = ""
    timeout: float = 30.0
    max_retries: int = 3
    
    # Status
    is_active: bool = True
    last_tested: Optional[str] = None
    test_status: str = "unknown"  # unknown, success, failed
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "name": self.name,
            "display_name": self.display_name,
            "provider_type": self.provider_type.value,
            "api_endpoint": self.api_endpoint,
            "api_key": self.api_key,
            "authentication_type": self.authentication_type.value,
            "custom_headers": self.custom_headers,
            "supported_models": self.supported_models,
            "capabilities": self.capabilities,
            "supports_vision": self.supports_vision,
            "supports_function_calling": self.supports_function_calling,
            "supports_streaming": self.supports_streaming,
            "rate_limits": {
                "requests_per_minute": self.rate_limits.requests_per_minute,
                "tokens_per_minute": self.rate_limits.tokens_per_minute,
                "concurrent_requests": self.rate_limits.concurrent_requests
            },
            "cost_config": {
                "input_cost_per_1k_tokens": self.cost_config.input_cost_per_1k_tokens,
                "output_cost_per_1k_tokens": self.cost_config.output_cost_per_1k_tokens,
                "image_cost_per_request": self.cost_config.image_cost_per_request,
                "currency": self.cost_config.currency
            },
            "default_model": self.default_model,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "is_active": self.is_active,
            "last_tested": self.last_tested,
            "test_status": self.test_status
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AIProvider':
        """Create from dictionary"""
        rate_limits_data = data.get("rate_limits", {})
        cost_config_data = data.get("cost_config", {})
        
        return cls(
            name=data["name"],
            display_name=data["display_name"],
            provider_type=ProviderType(data["provider_type"]),
            api_endpoint=data["api_endpoint"],
            api_key=data.get("api_key", ""),
            authentication_type=AuthenticationType(data.get("authentication_type", "api_key")),
            custom_headers=data.get("custom_headers", {}),
            supported_models=data.get("supported_models", []),
            capabilities=data.get("capabilities", []),
            supports_vision=data.get("supports_vision", False),
            supports_function_calling=data.get("supports_function_calling", False),
            supports_streaming=data.get("supports_streaming", True),
            rate_limits=RateLimit(
                requests_per_minute=rate_limits_data.get("requests_per_minute", 60),
                tokens_per_minute=rate_limits_data.get("tokens_per_minute", 100000),
                concurrent_requests=rate_limits_data.get("concurrent_requests", 10)
            ),
            cost_config=CostConfig(
                input_cost_per_1k_tokens=cost_config_data.get("input_cost_per_1k_tokens", 0.0),
                output_cost_per_1k_tokens=cost_config_data.get("output_cost_per_1k_tokens", 0.0),
                image_cost_per_request=cost_config_data.get("image_cost_per_request", 0.0),
                currency=cost_config_data.get("currency", "USD")
            ),
            default_model=data.get("default_model", ""),
            timeout=data.get("timeout", 30.0),
            max_retries=data.get("max_retries", 3),
            is_active=data.get("is_active", True),
            last_tested=data.get("last_tested"),
            test_status=data.get("test_status", "unknown")
        )

# Predefined provider configurations
DEFAULT_PROVIDERS: Dict[str, AIProvider] = {
    "openai": AIProvider(
        name="openai",
        display_name="OpenAI",
        provider_type=ProviderType.OPENAI,
        api_endpoint="https://api.openai.com/v1",
        supported_models=["gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini", "gpt-3.5-turbo"],
        capabilities=["text_generation", "vision", "code_generation", "function_calling"],
        supports_vision=True,
        supports_function_calling=True,
        default_model="gpt-4o-mini",
        cost_config=CostConfig(
            input_cost_per_1k_tokens=0.00015,
            output_cost_per_1k_tokens=0.0006
        )
    ),
    
    "anthropic": AIProvider(
        name="anthropic",
        display_name="Anthropic",
        provider_type=ProviderType.ANTHROPIC,
        api_endpoint="https://api.anthropic.com/v1",
        supported_models=["claude-3-5-sonnet-20241022", "claude-3-opus-20240229", "claude-3-haiku-20240307"],
        capabilities=["text_generation", "vision", "code_generation", "long_context"],
        supports_vision=True,
        supports_function_calling=True,
        default_model="claude-3-5-sonnet-20241022",
        cost_config=CostConfig(
            input_cost_per_1k_tokens=0.003,
            output_cost_per_1k_tokens=0.015
        )
    ),
    
    "openrouter": AIProvider(
        name="openrouter",
        display_name="OpenRouter",
        provider_type=ProviderType.OPENROUTER,
        api_endpoint="https://openrouter.ai/api/v1",
        supported_models=["mistral/mistral-large", "anthropic/claude-3.5-sonnet", "openai/gpt-4o"],
        capabilities=["text_generation", "vision", "code_generation"],
        supports_vision=True,
        supports_function_calling=True,
        default_model="mistral/mistral-large",
        cost_config=CostConfig(
            input_cost_per_1k_tokens=0.002,
            output_cost_per_1k_tokens=0.006
        )
    )
}

def get_default_providers() -> Dict[str, AIProvider]:
    """Get default provider configurations"""
    return DEFAULT_PROVIDERS.copy()

def get_provider_by_type(provider_type: ProviderType) -> Optional[AIProvider]:
    """Get default provider by type"""
    for provider in DEFAULT_PROVIDERS.values():
        if provider.provider_type == provider_type:
            return provider
    return None

def validate_provider_config(provider: AIProvider) -> List[str]:
    """Validate provider configuration and return list of errors"""
    errors = []
    
    if not provider.name:
        errors.append("Provider name is required")
    
    if not provider.display_name:
        errors.append("Provider display name is required")
    
    if not provider.api_endpoint:
        errors.append("API endpoint is required")
    
    if provider.authentication_type == AuthenticationType.API_KEY and not provider.api_key:
        errors.append("API key is required for API key authentication")
    
    if not provider.supported_models:
        errors.append("At least one supported model must be specified")
    
    if provider.default_model and provider.default_model not in provider.supported_models:
        errors.append("Default model must be in supported models list")
    
    return errors
