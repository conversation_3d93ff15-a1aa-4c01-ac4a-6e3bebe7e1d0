"""
Agent Configuration Panel for BlendPro v2.1.0
User interface for managing agent-model mappings and settings
"""

import bpy
from bpy.types import Panel, Operator, PropertyGroup
from bpy.props import StringProperty, BoolProperty, EnumProperty, FloatProperty, IntProperty
from typing import List, Tuple, Optional

from ..config.agent_configs import AgentType, AgentConfiguration, CostPriority, get_default_agent_configurations
from ..config.providers import ProviderType
from ..utils.provider_manager import get_provider_manager
from ..utils.logger import get_logger

class BLENDPRO_PG_AgentSettings(PropertyGroup):
    """Property group for agent settings"""
    
    # Agent selection
    agent_type: EnumProperty(
        name="Agent Type",
        description="Type of AI agent",
        items=[
            ('vision_specialist', 'Vision Specialist', 'Specialized in scene analysis and visual understanding'),
            ('code_architect', 'Code Architect', 'Specialized in code generation and architecture'),
            ('scene_optimizer', 'Scene Optimizer', 'Specialized in performance and workflow optimization'),
            ('ux_advisor', 'UX Advisor', 'Specialized in user experience improvements'),
            ('performance_analyst', 'Performance Analyst', 'Specialized in system performance analysis'),
            ('general_assistant', 'General Assistant', 'General purpose assistant')
        ],
        default='vision_specialist'
    )
    
    # Provider and model selection
    provider_name: StringProperty(
        name="Provider",
        description="AI provider for this agent",
        default=""
    )
    
    model_name: StringProperty(
        name="Model",
        description="AI model for this agent",
        default=""
    )
    
    # Model parameters
    max_tokens: IntProperty(
        name="Max Tokens",
        description="Maximum tokens for responses",
        default=2000,
        min=100,
        max=8000
    )
    
    temperature: FloatProperty(
        name="Temperature",
        description="Creativity/randomness of responses",
        default=0.7,
        min=0.0,
        max=2.0,
        precision=2
    )
    
    top_p: FloatProperty(
        name="Top P",
        description="Nucleus sampling parameter",
        default=1.0,
        min=0.0,
        max=1.0,
        precision=2
    )
    
    # Agent behavior
    confidence_threshold: FloatProperty(
        name="Confidence Threshold",
        description="Minimum confidence for agent selection",
        default=0.85,
        min=0.0,
        max=1.0,
        precision=2
    )
    
    # Fallback configuration
    fallback_provider: StringProperty(
        name="Fallback Provider",
        description="Fallback provider if primary fails",
        default=""
    )
    
    fallback_model: StringProperty(
        name="Fallback Model",
        description="Fallback model if primary fails",
        default=""
    )
    
    # Cost and performance settings
    cost_priority: EnumProperty(
        name="Cost Priority",
        description="Cost optimization priority",
        items=[
            ('cost', 'Cost', 'Prioritize lowest cost'),
            ('performance', 'Performance', 'Prioritize best performance'),
            ('balanced', 'Balanced', 'Balance cost and performance')
        ],
        default='balanced'
    )
    
    max_cost_per_request: FloatProperty(
        name="Max Cost per Request",
        description="Maximum cost per request (USD)",
        default=0.10,
        min=0.01,
        max=10.0,
        precision=4
    )
    
    # Status
    is_active: BoolProperty(
        name="Active",
        description="Agent is active and available",
        default=True
    )

def get_provider_items(self, context):
    """Get available providers for enum"""
    try:
        provider_manager = get_provider_manager()
        providers = provider_manager.get_active_providers()
        
        items = [('', 'Select Provider', 'Select an AI provider')]
        for provider_name, provider in providers.items():
            items.append((provider_name, provider.display_name, f"{provider.provider_type.value} provider"))
        
        return items
    except Exception:
        return [('', 'No Providers', 'No providers available')]

def get_model_items(self, context):
    """Get available models for selected provider"""
    try:
        settings = context.scene.blendpro_agent_settings
        if not settings.provider_name:
            return [('', 'Select Provider First', 'Select a provider first')]
        
        provider_manager = get_provider_manager()
        provider = provider_manager.get_provider(settings.provider_name)
        
        if not provider:
            return [('', 'Provider Not Found', 'Selected provider not found')]
        
        items = [('', 'Select Model', 'Select an AI model')]
        for model in provider.supported_models:
            items.append((model, model, f"Model: {model}"))
        
        return items
    except Exception:
        return [('', 'No Models', 'No models available')]

class BLENDPRO_OT_ConfigureAgent(Operator):
    """Configure AI agent"""
    bl_idname = "blendpro.configure_agent"
    bl_label = "Configure Agent"
    bl_description = "Configure AI agent with provider and model"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        try:
            settings = context.scene.blendpro_agent_settings
            provider_manager = get_provider_manager()
            
            # Create agent configuration
            agent_config = AgentConfiguration(
                agent_type=AgentType(settings.agent_type),
                provider_name=settings.provider_name,
                model_name=settings.model_name,
                max_tokens=settings.max_tokens,
                temperature=settings.temperature,
                top_p=settings.top_p,
                confidence_threshold=settings.confidence_threshold,
                fallback_provider=settings.fallback_provider if settings.fallback_provider else None,
                fallback_model=settings.fallback_model if settings.fallback_model else None,
                cost_priority=CostPriority(settings.cost_priority),
                max_cost_per_request=settings.max_cost_per_request,
                is_active=settings.is_active
            )
            
            # Configure agent
            success, errors = provider_manager.configure_agent(AgentType(settings.agent_type), agent_config)
            
            if success:
                self.report({'INFO'}, f"Agent '{settings.agent_type}' configured successfully")
            else:
                error_msg = "; ".join(errors)
                self.report({'ERROR'}, f"Failed to configure agent: {error_msg}")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Error configuring agent: {str(e)}")
            return {'CANCELLED'}

class BLENDPRO_OT_ResetAgentConfig(Operator):
    """Reset agent configuration to default"""
    bl_idname = "blendpro.reset_agent_config"
    bl_label = "Reset to Default"
    bl_description = "Reset agent configuration to default settings"
    bl_options = {'REGISTER', 'UNDO'}
    
    agent_type: StringProperty(name="Agent Type")
    
    def execute(self, context):
        try:
            provider_manager = get_provider_manager()
            success = provider_manager.reset_agent_config(AgentType(self.agent_type))
            
            if success:
                self.report({'INFO'}, f"Agent '{self.agent_type}' reset to default configuration")
            else:
                self.report({'ERROR'}, f"Failed to reset agent '{self.agent_type}'")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Error resetting agent: {str(e)}")
            return {'CANCELLED'}

class BLENDPRO_OT_LoadAgentConfig(Operator):
    """Load agent configuration"""
    bl_idname = "blendpro.load_agent_config"
    bl_label = "Load Configuration"
    bl_description = "Load agent configuration into form"
    bl_options = {'REGISTER'}
    
    agent_type: StringProperty(name="Agent Type")
    
    def execute(self, context):
        try:
            settings = context.scene.blendpro_agent_settings
            provider_manager = get_provider_manager()
            
            agent_config = provider_manager.get_agent_config(AgentType(self.agent_type))
            
            if agent_config:
                # Load configuration into form
                settings.agent_type = agent_config.agent_type.value
                settings.provider_name = agent_config.provider_name
                settings.model_name = agent_config.model_name
                settings.max_tokens = agent_config.max_tokens
                settings.temperature = agent_config.temperature
                settings.top_p = agent_config.top_p
                settings.confidence_threshold = agent_config.confidence_threshold
                settings.fallback_provider = agent_config.fallback_provider or ""
                settings.fallback_model = agent_config.fallback_model or ""
                settings.cost_priority = agent_config.cost_priority.value
                settings.max_cost_per_request = agent_config.max_cost_per_request
                settings.is_active = agent_config.is_active
                
                self.report({'INFO'}, f"Loaded configuration for agent '{self.agent_type}'")
            else:
                self.report({'ERROR'}, f"No configuration found for agent '{self.agent_type}'")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Error loading agent configuration: {str(e)}")
            return {'CANCELLED'}

class BLENDPRO_PT_AgentConfigPanel(Panel):
    """Agent Configuration Panel"""
    bl_label = "Agent Configuration"
    bl_idname = "BLENDPRO_PT_agent_config_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "BlendPro"
    bl_parent_id = "BLENDPRO_PT_main_panel"

    def draw(self, context):
        layout = self.layout
        settings = context.scene.blendpro_agent_settings

        # Agent overview section
        box = layout.box()
        box.label(text="Agent Overview", icon='COMMUNITY')

        try:
            provider_manager = get_provider_manager()
            agent_configs = provider_manager.get_all_agent_configs()

            if agent_configs:
                for agent_type, config in agent_configs.items():
                    row = box.row()

                    # Agent info
                    col = row.column()
                    status_icon = 'CHECKMARK' if config.is_active else 'X'
                    col.label(text=f"{agent_type.value.replace('_', ' ').title()}", icon=status_icon)
                    col.label(text=f"Provider: {config.provider_name}, Model: {config.model_name}")

                    # Actions
                    col = row.column()
                    load_op = col.operator("blendpro.load_agent_config", text="Edit", icon='SETTINGS')
                    load_op.agent_type = agent_type.value

                    reset_op = col.operator("blendpro.reset_agent_config", text="Reset", icon='LOOP_BACK')
                    reset_op.agent_type = agent_type.value
            else:
                box.label(text="No agent configurations found")

        except Exception as e:
            box.label(text=f"Error loading agents: {str(e)}", icon='ERROR')

        layout.separator()

        # Agent configuration form
        box = layout.box()
        box.label(text="Configure Agent", icon='SETTINGS')

        # Agent selection
        col = box.column()
        col.prop(settings, "agent_type")

        # Provider and model selection
        col.separator()
        col.label(text="Provider & Model", icon='NETWORK_DRIVE')

        # Dynamic provider selection
        provider_manager = get_provider_manager()
        providers = provider_manager.get_active_providers()

        if providers:
            provider_items = [(name, provider.display_name, f"{provider.provider_type.value} provider")
                            for name, provider in providers.items()]

            # Provider dropdown
            row = col.row()
            row.prop_search(settings, "provider_name", context.scene, "blendpro_providers", text="Provider")

            # Model dropdown (if provider selected)
            if settings.provider_name:
                provider = provider_manager.get_provider(settings.provider_name)
                if provider and provider.supported_models:
                    row = col.row()
                    row.prop_search(settings, "model_name", context.scene, "blendpro_models", text="Model")

                    # Show available models as buttons for easy selection
                    col.label(text="Available Models:")
                    flow = col.grid_flow(columns=2, align=True)
                    for model in provider.supported_models[:6]:  # Show first 6 models
                        op = flow.operator("blendpro.select_model", text=model)
                        op.model_name = model
        else:
            col.label(text="No active providers found", icon='ERROR')
            col.operator("blendpro.load_default_providers", text="Load Default Providers")

        # Model parameters
        col.separator()
        col.label(text="Model Parameters", icon='MODIFIER')
        col.prop(settings, "max_tokens")
        col.prop(settings, "temperature")
        col.prop(settings, "top_p")

        # Agent behavior
        col.separator()
        col.label(text="Agent Behavior", icon='TOOL_SETTINGS')
        col.prop(settings, "confidence_threshold")

        # Fallback configuration
        col.separator()
        col.label(text="Fallback Configuration", icon='RECOVER_LAST')
        col.prop(settings, "fallback_provider")
        col.prop(settings, "fallback_model")

        # Cost and performance
        col.separator()
        col.label(text="Cost & Performance", icon='FUND')
        col.prop(settings, "cost_priority")
        col.prop(settings, "max_cost_per_request")

        # Status
        col.separator()
        col.prop(settings, "is_active")

        # Configure button
        col.separator()
        col.operator("blendpro.configure_agent", text="Configure Agent", icon='CHECKMARK')

class BLENDPRO_OT_SelectModel(Operator):
    """Select model for agent"""
    bl_idname = "blendpro.select_model"
    bl_label = "Select Model"
    bl_description = "Select this model for the agent"
    bl_options = {'REGISTER'}

    model_name: StringProperty(name="Model Name")

    def execute(self, context):
        settings = context.scene.blendpro_agent_settings
        settings.model_name = self.model_name
        return {'FINISHED'}

def register():
    """Register UI classes"""
    bpy.utils.register_class(BLENDPRO_PG_AgentSettings)
    bpy.utils.register_class(BLENDPRO_OT_ConfigureAgent)
    bpy.utils.register_class(BLENDPRO_OT_ResetAgentConfig)
    bpy.utils.register_class(BLENDPRO_OT_LoadAgentConfig)
    bpy.utils.register_class(BLENDPRO_OT_SelectModel)
    bpy.utils.register_class(BLENDPRO_PT_AgentConfigPanel)

    # Register property group
    bpy.types.Scene.blendpro_agent_settings = bpy.props.PointerProperty(type=BLENDPRO_PG_AgentSettings)

def unregister():
    """Unregister UI classes"""
    bpy.utils.unregister_class(BLENDPRO_PT_AgentConfigPanel)
    bpy.utils.unregister_class(BLENDPRO_OT_SelectModel)
    bpy.utils.unregister_class(BLENDPRO_OT_LoadAgentConfig)
    bpy.utils.unregister_class(BLENDPRO_OT_ResetAgentConfig)
    bpy.utils.unregister_class(BLENDPRO_OT_ConfigureAgent)
    bpy.utils.unregister_class(BLENDPRO_PG_AgentSettings)

    # Unregister property group
    del bpy.types.Scene.blendpro_agent_settings
