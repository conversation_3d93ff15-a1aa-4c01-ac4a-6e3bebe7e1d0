"""
Spatial Reasoner for BlendPro v2.1.0
Advanced 3D spatial relationship analysis and reasoning
"""

import bpy
import bmesh
import mathutils
import time
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum

from ..config.settings import get_settings
from ..utils.logger import get_logger
from .depth_analyzer import get_depth_analyzer, DepthAnalysisResult

class SpatialRelation(Enum):
    """Types of spatial relationships"""
    ABOVE = "above"
    BELOW = "below"
    LEFT_OF = "left_of"
    RIGHT_OF = "right_of"
    IN_FRONT_OF = "in_front_of"
    BEHIND = "behind"
    INSIDE = "inside"
    CONTAINS = "contains"
    ADJACENT = "adjacent"
    OVERLAPPING = "overlapping"
    DISTANT = "distant"
    TOUCHING = "touching"

@dataclass
class SpatialObject:
    """Represents an object in 3D space with spatial properties"""
    name: str
    object_type: str
    location: mathutils.Vector
    dimensions: mathutils.Vector
    bounding_box: Tuple[mathutils.Vector, mathutils.Vector]  # min, max
    rotation: mathutils.Euler
    scale: mathutils.Vector
    volume: float
    surface_area: float
    center_of_mass: mathutils.Vector
    is_visible: bool
    is_selected: bool

@dataclass
class SpatialRelationship:
    """Represents a spatial relationship between two objects"""
    object_a: str
    object_b: str
    relation_type: SpatialRelation
    confidence: float
    distance: float
    relative_position: mathutils.Vector
    description: str

@dataclass
class SpatialGraph:
    """Graph representation of spatial relationships"""
    objects: Dict[str, SpatialObject]
    relationships: List[SpatialRelationship]
    spatial_clusters: List[List[str]]
    scene_bounds: Tuple[mathutils.Vector, mathutils.Vector]
    analysis_time: float

@dataclass
class SpatialAnalysisResult:
    """Result of spatial analysis"""
    spatial_graph: SpatialGraph
    depth_integration: Optional[DepthAnalysisResult]
    spatial_summary: str
    object_count: int
    relationship_count: int
    success: bool
    error: Optional[str] = None

class SpatialReasoner:
    """Analyzes and reasons about 3D spatial relationships"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger("BlendPro.SpatialReasoner")
        self.depth_analyzer = get_depth_analyzer()
        self._spatial_cache: Dict[str, Tuple[SpatialAnalysisResult, float]] = {}
        self._cache_timeout = 15.0  # seconds
    
    def analyze_spatial_relationships(
        self, 
        context, 
        include_depth: bool = True,
        use_cache: bool = True
    ) -> SpatialAnalysisResult:
        """Analyze spatial relationships in the scene"""
        
        start_time = time.time()
        
        # Generate cache key
        cache_key = self._generate_cache_key(context, include_depth)
        
        # Check cache
        if use_cache and cache_key in self._spatial_cache:
            cached_result, timestamp = self._spatial_cache[cache_key]
            if time.time() - timestamp < self._cache_timeout:
                self.logger.debug("Using cached spatial analysis")
                return cached_result
        
        try:
            # Extract spatial objects
            spatial_objects = self._extract_spatial_objects(context)
            
            if not spatial_objects:
                return SpatialAnalysisResult(
                    spatial_graph=SpatialGraph(
                        objects={},
                        relationships=[],
                        spatial_clusters=[],
                        scene_bounds=(mathutils.Vector(), mathutils.Vector()),
                        analysis_time=time.time() - start_time
                    ),
                    depth_integration=None,
                    spatial_summary="No objects found in scene",
                    object_count=0,
                    relationship_count=0,
                    success=True
                )
            
            # Calculate spatial relationships
            relationships = self._calculate_spatial_relationships(spatial_objects)
            
            # Find spatial clusters
            clusters = self._find_spatial_clusters(spatial_objects, relationships)
            
            # Calculate scene bounds
            scene_bounds = self._calculate_scene_bounds(spatial_objects)
            
            # Integrate depth information if requested
            depth_result = None
            if include_depth:
                depth_result = self.depth_analyzer.analyze_depth(context, use_cache=use_cache)
                if depth_result.success:
                    relationships = self._enhance_relationships_with_depth(
                        relationships, depth_result, spatial_objects
                    )
            
            # Create spatial graph
            spatial_graph = SpatialGraph(
                objects=spatial_objects,
                relationships=relationships,
                spatial_clusters=clusters,
                scene_bounds=scene_bounds,
                analysis_time=time.time() - start_time
            )
            
            # Generate summary
            summary = self._generate_spatial_summary(spatial_graph)
            
            result = SpatialAnalysisResult(
                spatial_graph=spatial_graph,
                depth_integration=depth_result,
                spatial_summary=summary,
                object_count=len(spatial_objects),
                relationship_count=len(relationships),
                success=True
            )
            
            # Cache result
            if use_cache:
                self._spatial_cache[cache_key] = (result, time.time())
            
            return result
            
        except Exception as e:
            self.logger.error(f"Spatial analysis failed: {e}")
            return SpatialAnalysisResult(
                spatial_graph=SpatialGraph(
                    objects={},
                    relationships=[],
                    spatial_clusters=[],
                    scene_bounds=(mathutils.Vector(), mathutils.Vector()),
                    analysis_time=time.time() - start_time
                ),
                depth_integration=None,
                spatial_summary=f"Analysis failed: {str(e)}",
                object_count=0,
                relationship_count=0,
                success=False,
                error=str(e)
            )
    
    def _extract_spatial_objects(self, context) -> Dict[str, SpatialObject]:
        """Extract spatial objects from the scene"""
        
        spatial_objects = {}
        
        try:
            for obj in context.scene.objects:
                if obj.type in ['MESH', 'CURVE', 'SURFACE', 'META', 'FONT']:
                    # Calculate bounding box
                    bbox_corners = [obj.matrix_world @ mathutils.Vector(corner) for corner in obj.bound_box]
                    min_corner = mathutils.Vector((
                        min(corner.x for corner in bbox_corners),
                        min(corner.y for corner in bbox_corners),
                        min(corner.z for corner in bbox_corners)
                    ))
                    max_corner = mathutils.Vector((
                        max(corner.x for corner in bbox_corners),
                        max(corner.y for corner in bbox_corners),
                        max(corner.z for corner in bbox_corners)
                    ))
                    
                    # Calculate volume and surface area
                    volume = self._calculate_object_volume(obj)
                    surface_area = self._calculate_object_surface_area(obj)
                    
                    # Calculate center of mass (approximation)
                    center_of_mass = obj.matrix_world @ obj.location
                    
                    spatial_obj = SpatialObject(
                        name=obj.name,
                        object_type=obj.type,
                        location=obj.matrix_world @ obj.location,
                        dimensions=mathutils.Vector(obj.dimensions),
                        bounding_box=(min_corner, max_corner),
                        rotation=obj.rotation_euler.copy(),
                        scale=obj.scale.copy(),
                        volume=volume,
                        surface_area=surface_area,
                        center_of_mass=center_of_mass,
                        is_visible=obj.visible_get(),
                        is_selected=obj.select_get()
                    )
                    
                    spatial_objects[obj.name] = spatial_obj
                    
        except Exception as e:
            self.logger.error(f"Object extraction failed: {e}")
        
        return spatial_objects
    
    def _calculate_object_volume(self, obj) -> float:
        """Calculate approximate volume of an object"""
        
        try:
            if obj.type == 'MESH' and obj.data:
                # Use bounding box volume as approximation
                dimensions = obj.dimensions
                return dimensions.x * dimensions.y * dimensions.z
            else:
                # Fallback to bounding box volume
                dimensions = obj.dimensions
                return dimensions.x * dimensions.y * dimensions.z
                
        except Exception:
            return 0.0
    
    def _calculate_object_surface_area(self, obj) -> float:
        """Calculate approximate surface area of an object"""
        
        try:
            if obj.type == 'MESH' and obj.data:
                # Use bounding box surface area as approximation
                d = obj.dimensions
                return 2 * (d.x * d.y + d.y * d.z + d.z * d.x)
            else:
                # Fallback to bounding box surface area
                d = obj.dimensions
                return 2 * (d.x * d.y + d.y * d.z + d.z * d.x)
                
        except Exception:
            return 0.0

    def _calculate_spatial_relationships(
        self,
        spatial_objects: Dict[str, SpatialObject]
    ) -> List[SpatialRelationship]:
        """Calculate spatial relationships between objects"""

        relationships = []
        object_names = list(spatial_objects.keys())

        try:
            for i, obj_a_name in enumerate(object_names):
                for obj_b_name in object_names[i+1:]:
                    obj_a = spatial_objects[obj_a_name]
                    obj_b = spatial_objects[obj_b_name]

                    # Calculate distance
                    distance = (obj_a.location - obj_b.location).length
                    relative_pos = obj_b.location - obj_a.location

                    # Determine spatial relationships
                    relations = self._determine_spatial_relations(obj_a, obj_b, distance, relative_pos)

                    for relation_type, confidence in relations:
                        relationship = SpatialRelationship(
                            object_a=obj_a_name,
                            object_b=obj_b_name,
                            relation_type=relation_type,
                            confidence=confidence,
                            distance=distance,
                            relative_position=relative_pos,
                            description=self._generate_relationship_description(
                                obj_a_name, obj_b_name, relation_type, distance
                            )
                        )
                        relationships.append(relationship)

        except Exception as e:
            self.logger.error(f"Relationship calculation failed: {e}")

        return relationships

    def _determine_spatial_relations(
        self,
        obj_a: SpatialObject,
        obj_b: SpatialObject,
        distance: float,
        relative_pos: mathutils.Vector
    ) -> List[Tuple[SpatialRelation, float]]:
        """Determine spatial relations between two objects"""

        relations = []

        try:
            # Distance-based relations
            avg_dimension = (obj_a.dimensions.length + obj_b.dimensions.length) / 2

            if distance < avg_dimension * 0.1:
                relations.append((SpatialRelation.TOUCHING, 0.9))
            elif distance < avg_dimension * 0.5:
                relations.append((SpatialRelation.ADJACENT, 0.8))
            elif distance > avg_dimension * 5:
                relations.append((SpatialRelation.DISTANT, 0.7))

            # Directional relations based on relative position
            abs_rel_pos = mathutils.Vector((abs(relative_pos.x), abs(relative_pos.y), abs(relative_pos.z)))
            max_axis = max(abs_rel_pos)

            if max_axis > 0:
                confidence = min(0.9, max_axis / distance) if distance > 0 else 0.5

                if abs(relative_pos.z) == max_axis:
                    if relative_pos.z > 0:
                        relations.append((SpatialRelation.ABOVE, confidence))
                    else:
                        relations.append((SpatialRelation.BELOW, confidence))

                elif abs(relative_pos.x) == max_axis:
                    if relative_pos.x > 0:
                        relations.append((SpatialRelation.RIGHT_OF, confidence))
                    else:
                        relations.append((SpatialRelation.LEFT_OF, confidence))

                elif abs(relative_pos.y) == max_axis:
                    if relative_pos.y > 0:
                        relations.append((SpatialRelation.IN_FRONT_OF, confidence))
                    else:
                        relations.append((SpatialRelation.BEHIND, confidence))

            # Containment relations
            if self._is_object_inside(obj_a, obj_b):
                relations.append((SpatialRelation.INSIDE, 0.8))
            elif self._is_object_inside(obj_b, obj_a):
                relations.append((SpatialRelation.CONTAINS, 0.8))

            # Overlapping relation
            if self._objects_overlap(obj_a, obj_b):
                relations.append((SpatialRelation.OVERLAPPING, 0.7))

        except Exception as e:
            self.logger.error(f"Spatial relation determination failed: {e}")

        return relations

    def _is_object_inside(self, inner_obj: SpatialObject, outer_obj: SpatialObject) -> bool:
        """Check if one object is inside another"""

        try:
            inner_min, inner_max = inner_obj.bounding_box
            outer_min, outer_max = outer_obj.bounding_box

            return (
                outer_min.x <= inner_min.x and inner_max.x <= outer_max.x and
                outer_min.y <= inner_min.y and inner_max.y <= outer_max.y and
                outer_min.z <= inner_min.z and inner_max.z <= outer_max.z
            )
        except Exception:
            return False

    def _objects_overlap(self, obj_a: SpatialObject, obj_b: SpatialObject) -> bool:
        """Check if two objects' bounding boxes overlap"""

        try:
            a_min, a_max = obj_a.bounding_box
            b_min, b_max = obj_b.bounding_box

            return (
                a_min.x < b_max.x and a_max.x > b_min.x and
                a_min.y < b_max.y and a_max.y > b_min.y and
                a_min.z < b_max.z and a_max.z > b_min.z
            )
        except Exception:
            return False

    def _generate_relationship_description(
        self,
        obj_a: str,
        obj_b: str,
        relation: SpatialRelation,
        distance: float
    ) -> str:
        """Generate human-readable description of spatial relationship"""

        distance_str = f"{distance:.2f} units"

        descriptions = {
            SpatialRelation.ABOVE: f"{obj_a} is above {obj_b} ({distance_str})",
            SpatialRelation.BELOW: f"{obj_a} is below {obj_b} ({distance_str})",
            SpatialRelation.LEFT_OF: f"{obj_a} is to the left of {obj_b} ({distance_str})",
            SpatialRelation.RIGHT_OF: f"{obj_a} is to the right of {obj_b} ({distance_str})",
            SpatialRelation.IN_FRONT_OF: f"{obj_a} is in front of {obj_b} ({distance_str})",
            SpatialRelation.BEHIND: f"{obj_a} is behind {obj_b} ({distance_str})",
            SpatialRelation.INSIDE: f"{obj_a} is inside {obj_b}",
            SpatialRelation.CONTAINS: f"{obj_a} contains {obj_b}",
            SpatialRelation.ADJACENT: f"{obj_a} is adjacent to {obj_b} ({distance_str})",
            SpatialRelation.OVERLAPPING: f"{obj_a} overlaps with {obj_b}",
            SpatialRelation.DISTANT: f"{obj_a} is distant from {obj_b} ({distance_str})",
            SpatialRelation.TOUCHING: f"{obj_a} is touching {obj_b}"
        }

        return descriptions.get(relation, f"{obj_a} has unknown relation to {obj_b}")

    def _find_spatial_clusters(
        self,
        spatial_objects: Dict[str, SpatialObject],
        relationships: List[SpatialRelationship]
    ) -> List[List[str]]:
        """Find clusters of spatially related objects"""

        clusters = []

        try:
            # Create adjacency graph
            adjacency = {name: set() for name in spatial_objects.keys()}

            for rel in relationships:
                if rel.relation_type in [SpatialRelation.ADJACENT, SpatialRelation.TOUCHING, SpatialRelation.OVERLAPPING]:
                    adjacency[rel.object_a].add(rel.object_b)
                    adjacency[rel.object_b].add(rel.object_a)

            # Find connected components
            visited = set()

            for obj_name in spatial_objects.keys():
                if obj_name not in visited:
                    cluster = []
                    self._dfs_cluster(obj_name, adjacency, visited, cluster)
                    if len(cluster) > 1:  # Only include clusters with multiple objects
                        clusters.append(cluster)

        except Exception as e:
            self.logger.error(f"Cluster finding failed: {e}")

        return clusters

    def _dfs_cluster(
        self,
        node: str,
        adjacency: Dict[str, Set[str]],
        visited: Set[str],
        cluster: List[str]
    ):
        """Depth-first search for cluster finding"""

        visited.add(node)
        cluster.append(node)

        for neighbor in adjacency[node]:
            if neighbor not in visited:
                self._dfs_cluster(neighbor, adjacency, visited, cluster)

    def _calculate_scene_bounds(self, spatial_objects: Dict[str, SpatialObject]) -> Tuple[mathutils.Vector, mathutils.Vector]:
        """Calculate overall scene bounding box"""

        if not spatial_objects:
            return (mathutils.Vector(), mathutils.Vector())

        try:
            all_min_corners = []
            all_max_corners = []

            for obj in spatial_objects.values():
                min_corner, max_corner = obj.bounding_box
                all_min_corners.append(min_corner)
                all_max_corners.append(max_corner)

            scene_min = mathutils.Vector((
                min(corner.x for corner in all_min_corners),
                min(corner.y for corner in all_min_corners),
                min(corner.z for corner in all_min_corners)
            ))

            scene_max = mathutils.Vector((
                max(corner.x for corner in all_max_corners),
                max(corner.y for corner in all_max_corners),
                max(corner.z for corner in all_max_corners)
            ))

            return (scene_min, scene_max)

        except Exception as e:
            self.logger.error(f"Scene bounds calculation failed: {e}")
            return (mathutils.Vector(), mathutils.Vector())

    def _enhance_relationships_with_depth(
        self,
        relationships: List[SpatialRelationship],
        depth_result: DepthAnalysisResult,
        spatial_objects: Dict[str, SpatialObject]
    ) -> List[SpatialRelationship]:
        """Enhance spatial relationships with depth information"""

        # For now, return relationships as-is
        # In a full implementation, we would use depth data to refine spatial relationships
        return relationships

    def _generate_spatial_summary(self, spatial_graph: SpatialGraph) -> str:
        """Generate human-readable summary of spatial analysis"""

        try:
            objects = spatial_graph.objects
            relationships = spatial_graph.relationships
            clusters = spatial_graph.spatial_clusters

            summary_parts = []

            # Object summary
            summary_parts.append(f"Scene contains {len(objects)} objects:")
            for obj_name, obj in objects.items():
                summary_parts.append(f"  - {obj_name} ({obj.object_type}) at {obj.location}")

            # Relationship summary
            if relationships:
                summary_parts.append(f"\nSpatial relationships ({len(relationships)} found):")
                for rel in relationships[:10]:  # Limit to first 10 relationships
                    summary_parts.append(f"  - {rel.description} (confidence: {rel.confidence:.2f})")

                if len(relationships) > 10:
                    summary_parts.append(f"  ... and {len(relationships) - 10} more relationships")

            # Cluster summary
            if clusters:
                summary_parts.append(f"\nSpatial clusters ({len(clusters)} found):")
                for i, cluster in enumerate(clusters):
                    summary_parts.append(f"  - Cluster {i+1}: {', '.join(cluster)}")

            # Scene bounds
            scene_min, scene_max = spatial_graph.scene_bounds
            scene_size = scene_max - scene_min
            summary_parts.append(f"\nScene dimensions: {scene_size.x:.2f} x {scene_size.y:.2f} x {scene_size.z:.2f} units")

            return "\n".join(summary_parts)

        except Exception as e:
            return f"Summary generation failed: {str(e)}"

    def _generate_cache_key(self, context, include_depth: bool) -> str:
        """Generate cache key for spatial analysis"""

        try:
            cache_components = [
                f"depth_{include_depth}",
                str(len(context.scene.objects)),
                str(context.scene.frame_current)
            ]

            # Add object positions (simplified)
            for obj in context.scene.objects:
                if obj.type in ['MESH', 'CURVE', 'SURFACE', 'META', 'FONT']:
                    loc = obj.location
                    cache_components.append(f"{obj.name}_{loc.x:.1f}_{loc.y:.1f}_{loc.z:.1f}")

            return "_".join(cache_components)

        except Exception:
            return f"fallback_{time.time()}"

    def get_relationships_by_type(
        self,
        result: SpatialAnalysisResult,
        relation_type: SpatialRelation
    ) -> List[SpatialRelationship]:
        """Get all relationships of a specific type"""

        if not result.success:
            return []

        return [rel for rel in result.spatial_graph.relationships if rel.relation_type == relation_type]

    def get_object_relationships(
        self,
        result: SpatialAnalysisResult,
        object_name: str
    ) -> List[SpatialRelationship]:
        """Get all relationships involving a specific object"""

        if not result.success:
            return []

        return [
            rel for rel in result.spatial_graph.relationships
            if rel.object_a == object_name or rel.object_b == object_name
        ]

    def clear_cache(self):
        """Clear spatial analysis cache"""
        self._spatial_cache.clear()
        self.logger.debug("Spatial analysis cache cleared")

# Global instance
_spatial_reasoner = None

def get_spatial_reasoner() -> SpatialReasoner:
    """Get global spatial reasoner instance"""
    global _spatial_reasoner
    if _spatial_reasoner is None:
        _spatial_reasoner = SpatialReasoner()
    return _spatial_reasoner
