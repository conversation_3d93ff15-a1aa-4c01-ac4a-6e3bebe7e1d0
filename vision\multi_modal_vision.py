"""
Multi-Modal Vision for BlendPro: AI Co-Pilot
Combines visual and textual analysis for comprehensive scene understanding
"""

import json
from typing import Dict, List, Any, Optional
import bpy

from ..config.settings import get_settings
from ..config.prompts import get_system_prompt, PromptType
from ..utils.api_client import get_api_client, APIRequest
from ..utils.dynamic_api_client import get_dynamic_api_client
from .scene_analyzer import get_scene_analyzer
from .context_extractor import get_context_extractor
from .screenshot_manager import get_screenshot_manager
from .immersive_vision_pipeline import get_immersive_vision_pipeline, AnalysisMode, PipelineConfiguration
from .depth_analyzer import get_depth_analyzer
from .spatial_reasoner import get_spatial_reasoner
from .temporal_tracker import get_temporal_tracker

class MultiModalVision:
    """Combines visual and textual scene analysis"""
    
    def __init__(self):
        self.settings = get_settings()
        self.api_client = get_api_client()
        self.dynamic_client = get_dynamic_api_client()
        self.scene_analyzer = get_scene_analyzer()
        self.context_extractor = get_context_extractor()
        self.screenshot_manager = get_screenshot_manager()

        # Enhanced vision components (v2.1.0)
        self.immersive_pipeline = get_immersive_vision_pipeline()
        self.depth_analyzer = get_depth_analyzer()
        self.spatial_reasoner = get_spatial_reasoner()
        self.temporal_tracker = get_temporal_tracker()

    def analyze_scene_with_enhanced_vision(
        self,
        context,
        user_query: Optional[str] = None,
        analysis_mode: AnalysisMode = AnalysisMode.STANDARD,
        include_legacy: bool = True
    ) -> Dict[str, Any]:
        """Enhanced scene analysis using immersive vision pipeline (v2.1.0)"""

        try:
            # Use immersive vision pipeline for comprehensive analysis
            immersive_result = self.immersive_pipeline.analyze_scene_immersively(
                context, mode=analysis_mode
            )

            if not immersive_result.success:
                # Fallback to legacy analysis
                if include_legacy:
                    return self.analyze_scene_with_vision(context, user_query, "auto")
                else:
                    return {"error": immersive_result.error, "fallback_used": False}

            # Use agent orchestrator if multi-agent system is enabled
            analysis_content = ""
            agent_info = {}

            if self.settings.enable_multi_agent_system:
                from ..config.agent_configs import AgentType
                from ..core.agent_orchestrator import get_agent_orchestrator

                agent_orchestrator = get_agent_orchestrator()

                # Prepare comprehensive context for vision specialist
                vision_context = {
                    "scene_understanding": immersive_result.scene_understanding,
                    "insights": [insight.__dict__ for insight in immersive_result.immersive_insights],
                    "recommendations": immersive_result.recommendations,
                    "depth_analysis": immersive_result.depth_result.__dict__ if immersive_result.depth_result else None,
                    "spatial_analysis": immersive_result.spatial_result.__dict__ if immersive_result.spatial_result else None,
                    "temporal_analysis": immersive_result.temporal_result.__dict__ if immersive_result.temporal_result else None
                }

                # Create enhanced query for vision specialist
                enhanced_query = user_query or "Analyze this Blender scene comprehensively"
                if immersive_result.immersive_insights:
                    high_priority_insights = [i for i in immersive_result.immersive_insights if i.priority >= 4]
                    if high_priority_insights:
                        enhanced_query += f"\n\nKey insights to consider: {', '.join([i.title for i in high_priority_insights])}"

                # Force use of VISION_SPECIALIST agent
                agent_response = agent_orchestrator.delegate_task(
                    enhanced_query,
                    vision_context,
                    force_agent=AgentType.VISION_SPECIALIST
                )

                if agent_response.success:
                    analysis_content = agent_response.content
                    agent_info = {
                        "agent_used": agent_response.agent_type.value,
                        "provider_used": agent_response.provider_used,
                        "model_used": agent_response.model_used,
                        "response_time": agent_response.response_time,
                        "cost_estimate": agent_response.cost_estimate
                    }
                else:
                    # Fallback to legacy vision system
                    if include_legacy:
                        return self.analyze_scene_with_vision(context, user_query, "auto")
                    else:
                        analysis_content = f"Agent analysis failed: {agent_response.error}"
            else:
                # Use legacy vision analysis
                if include_legacy:
                    legacy_result = self.analyze_scene_with_vision(context, user_query, "auto")
                    analysis_content = legacy_result.get("analysis", "Legacy analysis failed")
                else:
                    analysis_content = immersive_result.scene_understanding

            return {
                "analysis": analysis_content,
                "immersive_result": immersive_result,
                "scene_understanding": immersive_result.scene_understanding,
                "insights": immersive_result.immersive_insights,
                "recommendations": immersive_result.recommendations,
                "analysis_mode": analysis_mode.value,
                "performance_metrics": self.immersive_pipeline.get_performance_metrics(immersive_result),
                "agent_info": agent_info,
                "enhanced_vision_used": True,
                "fallback_used": False
            }

        except Exception as e:
            # Fallback to legacy analysis on error
            if include_legacy:
                legacy_result = self.analyze_scene_with_vision(context, user_query, "auto")
                legacy_result["enhanced_vision_error"] = str(e)
                legacy_result["fallback_used"] = True
                return legacy_result
            else:
                return {
                    "error": str(e),
                    "enhanced_vision_used": False,
                    "fallback_used": False
                }

    def analyze_scene_with_vision(
        self,
        context,
        user_query: Optional[str] = None,
        focus_type: str = "auto"
    ) -> Dict[str, Any]:
        """Perform comprehensive scene analysis using both vision and data"""
        
        try:
            # Get scene data
            scene_data = self.scene_analyzer.analyze_scene(context)
            
            # Extract relevant context
            context_data = self.context_extractor.extract_context(
                user_query or "analyze scene", 
                focus_type, 
                scene_data
            )
            
            # Capture screenshot if vision is enabled
            screenshot_data = None
            if self.settings.enable_vision_context:
                screenshot_data = self.screenshot_manager.capture_viewport_screenshot(context)
            
            # Perform vision analysis
            vision_analysis = self._perform_vision_analysis(
                context_data, 
                screenshot_data, 
                user_query
            )
            
            return {
                "scene_data": context_data,
                "screenshot": screenshot_data,
                "vision_analysis": vision_analysis,
                "analysis_type": "multi_modal"
            }
            
        except Exception as e:
            return {"error": f"Multi-modal analysis failed: {str(e)}"}
    
    def _perform_vision_analysis(
        self, 
        scene_data: Dict[str, Any], 
        screenshot_data: Optional[Dict[str, Any]],
        user_query: Optional[str] = None
    ) -> Dict[str, Any]:
        """Perform AI-powered vision analysis"""
        
        try:
            # Prepare the analysis prompt
            system_prompt = get_system_prompt(
                PromptType.VISION_ANALYZER,
                scene_data=json.dumps(scene_data, indent=2),
                visual_context="Screenshot provided" if screenshot_data else "No screenshot available",
                analysis_focus=user_query or "General scene analysis"
            )
            
            # Build the message content
            messages = [{"role": "system", "content": system_prompt}]
            
            # Add user query if provided
            if user_query:
                messages.append({
                    "role": "user", 
                    "content": f"Analyze the scene with focus on: {user_query}"
                })
            else:
                messages.append({
                    "role": "user",
                    "content": "Provide a comprehensive analysis of this Blender scene."
                })
            
            # Add screenshot if available
            if screenshot_data and not screenshot_data.get("error"):
                # For vision-capable models, add the image
                if self._is_vision_model_available():
                    messages[-1]["content"] = [
                        {"type": "text", "text": messages[-1]["content"]},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{screenshot_data['base64_image']}"
                            }
                        }
                    ]
            
            # Use agent orchestrator if multi-agent system is enabled
            if self.settings.enable_multi_agent_system:
                from ..config.agent_configs import AgentType
                from ..core.agent_orchestrator import get_agent_orchestrator

                agent_orchestrator = get_agent_orchestrator()

                # Prepare user input for vision specialist
                vision_query = user_query or "Analyze this Blender scene comprehensively"

                # Force use of VISION_SPECIALIST agent
                agent_response = agent_orchestrator.delegate_task(
                    vision_query,
                    context_data,
                    force_agent=AgentType.VISION_SPECIALIST
                )

                if agent_response.success:
                    return {
                        "analysis": agent_response.content,
                        "model_used": agent_response.model_used,
                        "provider_used": agent_response.provider_used,
                        "has_visual_input": screenshot_data is not None and not screenshot_data.get("error"),
                        "fallback_used": False,
                        "agent_used": agent_response.agent_type.value,
                        "response_time": agent_response.response_time,
                        "cost_estimate": agent_response.cost_estimate
                    }
                else:
                    # Fallback to legacy vision system
                    pass

            # Legacy vision analysis
            request = APIRequest(
                messages=messages,
                model=self._get_vision_model(),
                temperature=0.3,  # Lower temperature for more consistent analysis
                max_tokens=1000
            )

            response = self.api_client.make_request(request, use_vision=True)

            if response.error:
                return {"error": response.error, "fallback_used": False}

            return {
                "analysis": response.content,
                "model_used": request.model,
                "has_visual_input": screenshot_data is not None and not screenshot_data.get("error"),
                "fallback_used": False
            }
            
        except Exception as e:
            # Fallback to text-only analysis
            return self._fallback_text_analysis(scene_data, user_query, str(e))
    
    def _fallback_text_analysis(
        self, 
        scene_data: Dict[str, Any], 
        user_query: Optional[str],
        error_reason: str
    ) -> Dict[str, Any]:
        """Fallback to text-only analysis when vision fails"""
        
        try:
            # Use text-only model for analysis
            system_prompt = get_system_prompt(PromptType.VISION_ANALYZER)
            
            # Create detailed text description of scene
            scene_description = self._create_scene_description(scene_data)
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"""
Scene Description:
{scene_description}

Analysis Request: {user_query or 'General scene analysis'}

Please analyze this Blender scene based on the provided data.
"""}
            ]
            
            request = APIRequest(
                messages=messages,
                model=self.settings.custom_model if self.settings.use_custom_model else "gpt-4",
                temperature=0.3,
                max_tokens=800
            )
            
            response = self.api_client.make_request(request)
            
            return {
                "analysis": response.content if not response.error else "Analysis failed",
                "model_used": request.model,
                "has_visual_input": False,
                "fallback_used": True,
                "fallback_reason": error_reason
            }
            
        except Exception as e:
            return {
                "error": f"Both vision and fallback analysis failed: {str(e)}",
                "fallback_used": True
            }
    
    def _create_scene_description(self, scene_data: Dict[str, Any]) -> str:
        """Create detailed text description of scene data"""
        
        description_parts = []
        
        # Objects
        objects = scene_data.get("objects", [])
        if objects:
            description_parts.append(f"Scene contains {len(objects)} objects:")
            for obj in objects[:10]:  # Limit to first 10 objects
                obj_desc = f"- {obj['name']} ({obj['type']})"
                if obj.get("selected"):
                    obj_desc += " [SELECTED]"
                if obj.get("active"):
                    obj_desc += " [ACTIVE]"
                description_parts.append(obj_desc)
            
            if len(objects) > 10:
                description_parts.append(f"... and {len(objects) - 10} more objects")
        
        # Materials
        materials = scene_data.get("materials", [])
        if materials:
            description_parts.append(f"\nMaterials ({len(materials)}):")
            for mat in materials[:5]:  # Limit to first 5 materials
                description_parts.append(f"- {mat['name']} (users: {mat.get('users', 0)})")
        
        # Lights
        lights = scene_data.get("lights", [])
        if lights:
            description_parts.append(f"\nLighting ({len(lights)} lights):")
            for light in lights:
                description_parts.append(f"- {light['name']} ({light['light_type']}, energy: {light['energy']})")
        
        # Cameras
        cameras = scene_data.get("cameras", [])
        if cameras:
            description_parts.append(f"\nCameras ({len(cameras)}):")
            for cam in cameras:
                cam_desc = f"- {cam['name']} (lens: {cam['lens']}mm)"
                if cam.get("is_active"):
                    cam_desc += " [ACTIVE]"
                description_parts.append(cam_desc)
        
        # Context type
        context_type = scene_data.get("context_type")
        if context_type:
            description_parts.insert(0, f"Context focus: {context_type}")
        
        return "\n".join(description_parts)
    
    def _is_vision_model_available(self) -> bool:
        """Check if a vision-capable model is configured"""
        
        vision_config = self.settings.get_vision_api_config()
        model = vision_config.get("model", "")
        
        # Check if model supports vision
        vision_models = [
            "gpt-4-vision-preview", "gpt-4o", "gpt-4o-mini",
            "claude-3-5-sonnet", "claude-3-opus", "claude-3-sonnet", "claude-3-haiku"
        ]
        
        return any(vm in model for vm in vision_models)
    
    def _get_vision_model(self) -> str:
        """Get the configured vision model"""
        
        vision_config = self.settings.get_vision_api_config()
        return vision_config.get("model", "gpt-4-vision-preview")
    
    def analyze_spatial_relationships(
        self, 
        context, 
        objects: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Analyze spatial relationships between objects"""
        
        try:
            # Get scene data
            scene_data = self.scene_analyzer.analyze_scene(context)
            
            # Filter objects if specified
            if objects:
                filtered_objects = [
                    obj for obj in scene_data.get("objects", [])
                    if obj["name"] in objects
                ]
            else:
                filtered_objects = scene_data.get("objects", [])
            
            # Analyze relationships
            relationships = []
            
            for i, obj1 in enumerate(filtered_objects):
                for obj2 in filtered_objects[i+1:]:
                    relationship = self._calculate_spatial_relationship(obj1, obj2)
                    if relationship:
                        relationships.append(relationship)
            
            return {
                "relationships": relationships,
                "analyzed_objects": len(filtered_objects),
                "total_relationships": len(relationships)
            }
            
        except Exception as e:
            return {"error": f"Spatial analysis failed: {str(e)}"}
    
    def _calculate_spatial_relationship(self, obj1: Dict[str, Any], obj2: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Calculate spatial relationship between two objects"""
        
        try:
            from mathutils import Vector
            
            pos1 = Vector(obj1["location"])
            pos2 = Vector(obj2["location"])
            
            distance = (pos1 - pos2).length
            direction = (pos2 - pos1).normalized()
            
            # Determine relative position
            relative_pos = "near"
            if distance > 5.0:
                relative_pos = "far"
            elif distance > 2.0:
                relative_pos = "medium"
            
            # Determine direction
            if abs(direction.z) > 0.7:
                spatial_dir = "above" if direction.z > 0 else "below"
            elif abs(direction.x) > abs(direction.y):
                spatial_dir = "right" if direction.x > 0 else "left"
            else:
                spatial_dir = "front" if direction.y > 0 else "back"
            
            return {
                "object1": obj1["name"],
                "object2": obj2["name"],
                "distance": round(distance, 2),
                "relative_position": relative_pos,
                "direction": spatial_dir,
                "relationship": f"{obj1['name']} is {relative_pos} and to the {spatial_dir} of {obj2['name']}"
            }
            
        except Exception:
            return None
    
    def get_vision_capabilities(self) -> Dict[str, Any]:
        """Get information about available vision capabilities"""
        
        return {
            "vision_model_available": self._is_vision_model_available(),
            "screenshot_available": self.screenshot_manager is not None,
            "scene_analysis_available": True,
            "spatial_analysis_available": True,
            "multi_view_available": True,
            "configured_vision_model": self._get_vision_model(),
            "vision_context_enabled": self.settings.enable_vision_context
        }

    # Enhanced Vision Methods (v2.1.0)

    def analyze_depth_only(self, context, sample_density: float = 1.0) -> Dict[str, Any]:
        """Perform depth-only analysis"""

        try:
            depth_result = self.depth_analyzer.analyze_depth(
                context, sample_density=sample_density
            )

            if depth_result.success:
                return {
                    "depth_analysis": depth_result,
                    "summary": self.depth_analyzer.get_depth_statistics_summary(depth_result),
                    "success": True
                }
            else:
                return {"error": depth_result.error, "success": False}

        except Exception as e:
            return {"error": str(e), "success": False}

    def analyze_spatial_only(self, context, include_depth: bool = True) -> Dict[str, Any]:
        """Perform spatial-only analysis"""

        try:
            spatial_result = self.spatial_reasoner.analyze_spatial_relationships(
                context, include_depth=include_depth
            )

            if spatial_result.success:
                return {
                    "spatial_analysis": spatial_result,
                    "summary": spatial_result.spatial_summary,
                    "success": True
                }
            else:
                return {"error": spatial_result.error, "success": False}

        except Exception as e:
            return {"error": str(e), "success": False}

    def analyze_temporal_only(self, context, include_spatial: bool = True, include_depth: bool = False) -> Dict[str, Any]:
        """Perform temporal-only analysis"""

        try:
            temporal_result = self.temporal_tracker.analyze_temporal_changes(
                context, include_spatial=include_spatial, include_depth=include_depth
            )

            if temporal_result.success:
                return {
                    "temporal_analysis": temporal_result,
                    "summary": temporal_result.change_summary,
                    "success": True
                }
            else:
                return {"error": temporal_result.error, "success": False}

        except Exception as e:
            return {"error": str(e), "success": False}

    def get_scene_insights(self, context, analysis_mode: AnalysisMode = AnalysisMode.QUICK) -> Dict[str, Any]:
        """Get quick scene insights using immersive pipeline"""

        try:
            result = self.immersive_pipeline.analyze_scene_immersively(context, mode=analysis_mode)

            if result.success:
                return {
                    "insights": result.immersive_insights,
                    "recommendations": result.recommendations,
                    "scene_understanding": result.scene_understanding,
                    "analysis_time": result.total_analysis_time,
                    "success": True
                }
            else:
                return {"error": result.error, "success": False}

        except Exception as e:
            return {"error": str(e), "success": False}

    def compare_scene_states(self, context) -> Dict[str, Any]:
        """Compare current scene with previous state"""

        try:
            temporal_result = self.temporal_tracker.analyze_temporal_changes(context)

            if temporal_result.success:
                changes = temporal_result.changes
                significant_changes = [c for c in changes if c.confidence > 0.8]

                return {
                    "total_changes": len(changes),
                    "significant_changes": len(significant_changes),
                    "change_summary": temporal_result.change_summary,
                    "changes": changes,
                    "current_state": temporal_result.current_state,
                    "previous_state": temporal_result.previous_state,
                    "success": True
                }
            else:
                return {"error": temporal_result.error, "success": False}

        except Exception as e:
            return {"error": str(e), "success": False}

    def clear_all_vision_caches(self):
        """Clear all vision component caches"""

        try:
            self.depth_analyzer.clear_cache()
            self.spatial_reasoner.clear_cache()
            self.temporal_tracker.clear_history()
            self.immersive_pipeline.clear_cache()
            return {"success": True, "message": "All vision caches cleared"}

        except Exception as e:
            return {"success": False, "error": str(e)}

# Global multi-modal vision instance
_multi_modal_vision: Optional[MultiModalVision] = None

def get_multi_modal_vision() -> MultiModalVision:
    """Get global multi-modal vision instance"""
    global _multi_modal_vision
    if _multi_modal_vision is None:
        _multi_modal_vision = MultiModalVision()
    return _multi_modal_vision
