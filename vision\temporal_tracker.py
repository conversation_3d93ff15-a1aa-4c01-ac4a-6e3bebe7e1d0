"""
Temporal Tracker for BlendPro v2.1.0
Advanced temporal change detection and scene state tracking
"""

import bpy
import time
import json
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime

from ..config.settings import get_settings
from ..utils.logger import get_logger
from .spatial_reasoner import get_spatial_reasoner, SpatialAnalysisResult
from .depth_analyzer import get_depth_analyzer, DepthAnalysisResult

class ChangeType(Enum):
    """Types of changes that can be detected"""
    OBJECT_ADDED = "object_added"
    OBJECT_REMOVED = "object_removed"
    OBJECT_MOVED = "object_moved"
    OBJECT_ROTATED = "object_rotated"
    OBJECT_SCALED = "object_scaled"
    OBJECT_MODIFIED = "object_modified"
    MATERIAL_CHANGED = "material_changed"
    LIGHTING_CHANGED = "lighting_changed"
    CAMERA_MOVED = "camera_moved"
    VIEWPORT_CHANGED = "viewport_changed"
    RELATIONSHIP_CHANGED = "relationship_changed"

@dataclass
class ObjectState:
    """Represents the state of an object at a point in time"""
    name: str
    object_type: str
    location: Tuple[float, float, float]
    rotation: Tuple[float, float, float]
    scale: Tuple[float, float, float]
    dimensions: Tuple[float, float, float]
    visible: bool
    selected: bool
    material_count: int
    modifier_count: int
    vertex_count: Optional[int] = None
    face_count: Optional[int] = None

@dataclass
class SceneState:
    """Represents the complete state of a scene at a point in time"""
    timestamp: float
    frame_number: int
    objects: Dict[str, ObjectState]
    camera_location: Optional[Tuple[float, float, float]]
    camera_rotation: Optional[Tuple[float, float, float]]
    active_object: Optional[str]
    selected_objects: List[str]
    viewport_shading: Optional[str]
    scene_hash: str

@dataclass
class Change:
    """Represents a detected change"""
    change_type: ChangeType
    object_name: Optional[str]
    timestamp: float
    frame_number: int
    description: str
    old_value: Any
    new_value: Any
    confidence: float

@dataclass
class TemporalAnalysisResult:
    """Result of temporal analysis"""
    current_state: SceneState
    previous_state: Optional[SceneState]
    changes: List[Change]
    change_summary: str
    spatial_changes: Optional[Dict[str, Any]]
    depth_changes: Optional[Dict[str, Any]]
    analysis_time: float
    success: bool
    error: Optional[str] = None

class TemporalTracker:
    """Tracks changes in the scene over time"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger("BlendPro.TemporalTracker")
        self.spatial_reasoner = get_spatial_reasoner()
        self.depth_analyzer = get_depth_analyzer()
        
        # State history
        self._state_history: List[SceneState] = []
        self._max_history_size = 50  # Keep last 50 states
        
        # Change detection settings
        self._position_threshold = 0.01  # Minimum movement to detect
        self._rotation_threshold = 0.01  # Minimum rotation to detect
        self._scale_threshold = 0.01    # Minimum scale change to detect
    
    def analyze_temporal_changes(
        self, 
        context, 
        include_spatial: bool = True,
        include_depth: bool = False
    ) -> TemporalAnalysisResult:
        """Analyze temporal changes in the scene"""
        
        start_time = time.time()
        
        try:
            # Capture current scene state
            current_state = self._capture_scene_state(context)
            
            # Get previous state for comparison
            previous_state = self._get_previous_state()
            
            # Detect changes
            changes = []
            if previous_state:
                changes = self._detect_changes(previous_state, current_state)
            
            # Analyze spatial changes if requested
            spatial_changes = None
            if include_spatial:
                spatial_changes = self._analyze_spatial_changes(context, previous_state, current_state)
            
            # Analyze depth changes if requested
            depth_changes = None
            if include_depth:
                depth_changes = self._analyze_depth_changes(context, previous_state, current_state)
            
            # Generate change summary
            change_summary = self._generate_change_summary(changes, spatial_changes, depth_changes)
            
            # Store current state in history
            self._add_state_to_history(current_state)
            
            result = TemporalAnalysisResult(
                current_state=current_state,
                previous_state=previous_state,
                changes=changes,
                change_summary=change_summary,
                spatial_changes=spatial_changes,
                depth_changes=depth_changes,
                analysis_time=time.time() - start_time,
                success=True
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Temporal analysis failed: {e}")
            return TemporalAnalysisResult(
                current_state=SceneState(
                    timestamp=time.time(),
                    frame_number=0,
                    objects={},
                    camera_location=None,
                    camera_rotation=None,
                    active_object=None,
                    selected_objects=[],
                    viewport_shading=None,
                    scene_hash=""
                ),
                previous_state=None,
                changes=[],
                change_summary=f"Analysis failed: {str(e)}",
                spatial_changes=None,
                depth_changes=None,
                analysis_time=time.time() - start_time,
                success=False,
                error=str(e)
            )
    
    def _capture_scene_state(self, context) -> SceneState:
        """Capture the current state of the scene"""
        
        timestamp = time.time()
        frame_number = context.scene.frame_current
        
        # Capture object states
        objects = {}
        for obj in context.scene.objects:
            obj_state = ObjectState(
                name=obj.name,
                object_type=obj.type,
                location=tuple(obj.location),
                rotation=tuple(obj.rotation_euler),
                scale=tuple(obj.scale),
                dimensions=tuple(obj.dimensions),
                visible=obj.visible_get(),
                selected=obj.select_get(),
                material_count=len(obj.material_slots),
                modifier_count=len(obj.modifiers)
            )
            
            # Add mesh-specific data
            if obj.type == 'MESH' and obj.data:
                obj_state.vertex_count = len(obj.data.vertices)
                obj_state.face_count = len(obj.data.polygons)
            
            objects[obj.name] = obj_state
        
        # Capture camera state
        camera_location = None
        camera_rotation = None
        if context.scene.camera:
            camera_location = tuple(context.scene.camera.location)
            camera_rotation = tuple(context.scene.camera.rotation_euler)
        
        # Capture selection state
        active_object = context.active_object.name if context.active_object else None
        selected_objects = [obj.name for obj in context.selected_objects]
        
        # Capture viewport state
        viewport_shading = None
        if context.area and context.area.type == 'VIEW_3D':
            space = context.area.spaces.active
            if space and hasattr(space, 'shading'):
                viewport_shading = space.shading.type
        
        # Generate scene hash for quick comparison
        scene_hash = self._generate_scene_hash(objects, camera_location, camera_rotation)
        
        return SceneState(
            timestamp=timestamp,
            frame_number=frame_number,
            objects=objects,
            camera_location=camera_location,
            camera_rotation=camera_rotation,
            active_object=active_object,
            selected_objects=selected_objects,
            viewport_shading=viewport_shading,
            scene_hash=scene_hash
        )
    
    def _generate_scene_hash(
        self, 
        objects: Dict[str, ObjectState], 
        camera_location: Optional[Tuple[float, float, float]], 
        camera_rotation: Optional[Tuple[float, float, float]]
    ) -> str:
        """Generate a hash representing the scene state"""
        
        try:
            # Create a simplified representation for hashing
            hash_data = {
                "objects": {
                    name: {
                        "location": obj.location,
                        "rotation": obj.rotation,
                        "scale": obj.scale,
                        "type": obj.object_type
                    }
                    for name, obj in objects.items()
                },
                "camera_location": camera_location,
                "camera_rotation": camera_rotation
            }
            
            # Convert to JSON string and hash
            json_str = json.dumps(hash_data, sort_keys=True)
            return hashlib.md5(json_str.encode()).hexdigest()
            
        except Exception:
            return f"hash_error_{time.time()}"

    def _get_previous_state(self) -> Optional[SceneState]:
        """Get the most recent previous state"""

        if self._state_history:
            return self._state_history[-1]
        return None

    def _add_state_to_history(self, state: SceneState):
        """Add state to history, maintaining size limit"""

        self._state_history.append(state)

        # Maintain history size limit
        if len(self._state_history) > self._max_history_size:
            self._state_history.pop(0)

    def _detect_changes(self, previous_state: SceneState, current_state: SceneState) -> List[Change]:
        """Detect changes between two scene states"""

        changes = []

        try:
            # Quick check using scene hash
            if previous_state.scene_hash == current_state.scene_hash:
                return changes  # No changes detected

            # Detect object changes
            prev_objects = previous_state.objects
            curr_objects = current_state.objects

            # Find added objects
            for obj_name in curr_objects:
                if obj_name not in prev_objects:
                    changes.append(Change(
                        change_type=ChangeType.OBJECT_ADDED,
                        object_name=obj_name,
                        timestamp=current_state.timestamp,
                        frame_number=current_state.frame_number,
                        description=f"Object '{obj_name}' was added to the scene",
                        old_value=None,
                        new_value=asdict(curr_objects[obj_name]),
                        confidence=1.0
                    ))

            # Find removed objects
            for obj_name in prev_objects:
                if obj_name not in curr_objects:
                    changes.append(Change(
                        change_type=ChangeType.OBJECT_REMOVED,
                        object_name=obj_name,
                        timestamp=current_state.timestamp,
                        frame_number=current_state.frame_number,
                        description=f"Object '{obj_name}' was removed from the scene",
                        old_value=asdict(prev_objects[obj_name]),
                        new_value=None,
                        confidence=1.0
                    ))

            # Find modified objects
            for obj_name in curr_objects:
                if obj_name in prev_objects:
                    prev_obj = prev_objects[obj_name]
                    curr_obj = curr_objects[obj_name]

                    # Check for position changes
                    if self._vector_changed(prev_obj.location, curr_obj.location, self._position_threshold):
                        changes.append(Change(
                            change_type=ChangeType.OBJECT_MOVED,
                            object_name=obj_name,
                            timestamp=current_state.timestamp,
                            frame_number=current_state.frame_number,
                            description=f"Object '{obj_name}' moved from {prev_obj.location} to {curr_obj.location}",
                            old_value=prev_obj.location,
                            new_value=curr_obj.location,
                            confidence=0.9
                        ))

                    # Check for rotation changes
                    if self._vector_changed(prev_obj.rotation, curr_obj.rotation, self._rotation_threshold):
                        changes.append(Change(
                            change_type=ChangeType.OBJECT_ROTATED,
                            object_name=obj_name,
                            timestamp=current_state.timestamp,
                            frame_number=current_state.frame_number,
                            description=f"Object '{obj_name}' rotated from {prev_obj.rotation} to {curr_obj.rotation}",
                            old_value=prev_obj.rotation,
                            new_value=curr_obj.rotation,
                            confidence=0.9
                        ))

                    # Check for scale changes
                    if self._vector_changed(prev_obj.scale, curr_obj.scale, self._scale_threshold):
                        changes.append(Change(
                            change_type=ChangeType.OBJECT_SCALED,
                            object_name=obj_name,
                            timestamp=current_state.timestamp,
                            frame_number=current_state.frame_number,
                            description=f"Object '{obj_name}' scaled from {prev_obj.scale} to {curr_obj.scale}",
                            old_value=prev_obj.scale,
                            new_value=curr_obj.scale,
                            confidence=0.9
                        ))

                    # Check for mesh modifications
                    if (prev_obj.vertex_count != curr_obj.vertex_count or
                        prev_obj.face_count != curr_obj.face_count):
                        changes.append(Change(
                            change_type=ChangeType.OBJECT_MODIFIED,
                            object_name=obj_name,
                            timestamp=current_state.timestamp,
                            frame_number=current_state.frame_number,
                            description=f"Object '{obj_name}' geometry was modified",
                            old_value={"vertices": prev_obj.vertex_count, "faces": prev_obj.face_count},
                            new_value={"vertices": curr_obj.vertex_count, "faces": curr_obj.face_count},
                            confidence=0.8
                        ))

                    # Check for material changes
                    if prev_obj.material_count != curr_obj.material_count:
                        changes.append(Change(
                            change_type=ChangeType.MATERIAL_CHANGED,
                            object_name=obj_name,
                            timestamp=current_state.timestamp,
                            frame_number=current_state.frame_number,
                            description=f"Object '{obj_name}' materials changed",
                            old_value=prev_obj.material_count,
                            new_value=curr_obj.material_count,
                            confidence=0.8
                        ))

            # Check for camera changes
            if (previous_state.camera_location != current_state.camera_location or
                previous_state.camera_rotation != current_state.camera_rotation):
                changes.append(Change(
                    change_type=ChangeType.CAMERA_MOVED,
                    object_name=None,
                    timestamp=current_state.timestamp,
                    frame_number=current_state.frame_number,
                    description="Camera position or rotation changed",
                    old_value={"location": previous_state.camera_location, "rotation": previous_state.camera_rotation},
                    new_value={"location": current_state.camera_location, "rotation": current_state.camera_rotation},
                    confidence=0.9
                ))

            # Check for viewport changes
            if previous_state.viewport_shading != current_state.viewport_shading:
                changes.append(Change(
                    change_type=ChangeType.VIEWPORT_CHANGED,
                    object_name=None,
                    timestamp=current_state.timestamp,
                    frame_number=current_state.frame_number,
                    description=f"Viewport shading changed from {previous_state.viewport_shading} to {current_state.viewport_shading}",
                    old_value=previous_state.viewport_shading,
                    new_value=current_state.viewport_shading,
                    confidence=0.7
                ))

        except Exception as e:
            self.logger.error(f"Change detection failed: {e}")

        return changes

    def _vector_changed(self, vec1: Tuple[float, float, float], vec2: Tuple[float, float, float], threshold: float) -> bool:
        """Check if two vectors differ by more than threshold"""

        try:
            for i in range(3):
                if abs(vec1[i] - vec2[i]) > threshold:
                    return True
            return False
        except Exception:
            return False

    def _analyze_spatial_changes(
        self,
        context,
        previous_state: Optional[SceneState],
        current_state: SceneState
    ) -> Optional[Dict[str, Any]]:
        """Analyze changes in spatial relationships"""

        try:
            # Get current spatial analysis
            current_spatial = self.spatial_reasoner.analyze_spatial_relationships(context, include_depth=False)

            if not current_spatial.success:
                return None

            spatial_changes = {
                "current_relationships": len(current_spatial.spatial_graph.relationships),
                "current_clusters": len(current_spatial.spatial_graph.spatial_clusters),
                "relationship_changes": [],
                "new_relationships": [],
                "lost_relationships": []
            }

            # For now, just return current state
            # In a full implementation, we would compare with previous spatial state
            return spatial_changes

        except Exception as e:
            self.logger.error(f"Spatial change analysis failed: {e}")
            return None

    def _analyze_depth_changes(
        self,
        context,
        previous_state: Optional[SceneState],
        current_state: SceneState
    ) -> Optional[Dict[str, Any]]:
        """Analyze changes in depth information"""

        try:
            # Get current depth analysis
            current_depth = self.depth_analyzer.analyze_depth(context)

            if not current_depth.success:
                return None

            depth_changes = {
                "depth_range_changed": False,
                "depth_statistics": current_depth.depth_statistics,
                "depth_regions": len(current_depth.depth_regions),
                "depth_points": len(current_depth.depth_points)
            }

            # For now, just return current state
            # In a full implementation, we would compare with previous depth state
            return depth_changes

        except Exception as e:
            self.logger.error(f"Depth change analysis failed: {e}")
            return None

    def _generate_change_summary(
        self,
        changes: List[Change],
        spatial_changes: Optional[Dict[str, Any]],
        depth_changes: Optional[Dict[str, Any]]
    ) -> str:
        """Generate human-readable summary of changes"""

        if not changes and not spatial_changes and not depth_changes:
            return "No changes detected in the scene"

        summary_parts = []

        # Object changes summary
        if changes:
            change_counts = {}
            for change in changes:
                change_type = change.change_type.value
                change_counts[change_type] = change_counts.get(change_type, 0) + 1

            summary_parts.append(f"Detected {len(changes)} changes:")
            for change_type, count in change_counts.items():
                summary_parts.append(f"  - {change_type.replace('_', ' ').title()}: {count}")

            # Highlight significant changes
            significant_changes = [c for c in changes if c.confidence > 0.8]
            if significant_changes:
                summary_parts.append(f"\nSignificant changes:")
                for change in significant_changes[:5]:  # Show first 5
                    summary_parts.append(f"  - {change.description}")

        # Spatial changes summary
        if spatial_changes:
            summary_parts.append(f"\nSpatial analysis:")
            summary_parts.append(f"  - Current relationships: {spatial_changes.get('current_relationships', 0)}")
            summary_parts.append(f"  - Current clusters: {spatial_changes.get('current_clusters', 0)}")

        # Depth changes summary
        if depth_changes:
            summary_parts.append(f"\nDepth analysis:")
            summary_parts.append(f"  - Depth regions: {depth_changes.get('depth_regions', 0)}")
            summary_parts.append(f"  - Depth points: {depth_changes.get('depth_points', 0)}")

        return "\n".join(summary_parts)

    def get_change_history(self, change_type: Optional[ChangeType] = None) -> List[Change]:
        """Get history of changes, optionally filtered by type"""

        all_changes = []

        # Extract changes from state history
        for i in range(1, len(self._state_history)):
            prev_state = self._state_history[i-1]
            curr_state = self._state_history[i]
            changes = self._detect_changes(prev_state, curr_state)
            all_changes.extend(changes)

        # Filter by type if specified
        if change_type:
            all_changes = [c for c in all_changes if c.change_type == change_type]

        return all_changes

    def get_object_change_history(self, object_name: str) -> List[Change]:
        """Get change history for a specific object"""

        all_changes = self.get_change_history()
        return [c for c in all_changes if c.object_name == object_name]

    def clear_history(self):
        """Clear the state history"""

        self._state_history.clear()
        self.logger.debug("Temporal tracking history cleared")

    def get_state_count(self) -> int:
        """Get number of states in history"""

        return len(self._state_history)

    def set_change_thresholds(
        self,
        position_threshold: float = None,
        rotation_threshold: float = None,
        scale_threshold: float = None
    ):
        """Set thresholds for change detection"""

        if position_threshold is not None:
            self._position_threshold = position_threshold
        if rotation_threshold is not None:
            self._rotation_threshold = rotation_threshold
        if scale_threshold is not None:
            self._scale_threshold = scale_threshold

        self.logger.debug(f"Change thresholds updated: pos={self._position_threshold}, rot={self._rotation_threshold}, scale={self._scale_threshold}")

# Global instance
_temporal_tracker = None

def get_temporal_tracker() -> TemporalTracker:
    """Get global temporal tracker instance"""
    global _temporal_tracker
    if _temporal_tracker is None:
        _temporal_tracker = TemporalTracker()
    return _temporal_tracker
